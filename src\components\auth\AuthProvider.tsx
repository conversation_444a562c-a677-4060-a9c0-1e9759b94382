'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { AuthService } from '@/lib/auth'
import type { AuthUser } from '@/lib/auth'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<any>
  signUp: (email: string, password: string, fullName: string, userType?: 'student' | 'teacher' | 'admin') => Promise<any>
  signOut: () => Promise<void>
  hasPermission: (role: 'student' | 'teacher' | 'admin') => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const currentUser = await AuthService.getCurrentUser()
        setUser(currentUser)
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session) {
        const currentUser = await AuthService.getCurrentUser()
        setUser(currentUser)
      } else if (event === 'SIGNED_OUT') {
        setUser(null)
      }
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    const result = await AuthService.signIn(email, password)
    if (result.data && !result.error) {
      const currentUser = await AuthService.getCurrentUser()
      setUser(currentUser)
    }
    return result
  }

  const signUp = async (email: string, password: string, fullName: string, userType: 'student' | 'teacher' | 'admin' = 'student') => {
    const result = await AuthService.signUp(email, password, fullName, userType)
    if (result.data && !result.error) {
      const currentUser = await AuthService.getCurrentUser()
      setUser(currentUser)
    }
    return result
  }

  const signOut = async () => {
    await AuthService.signOut()
    setUser(null)
  }

  const hasPermission = (requiredRole: 'student' | 'teacher' | 'admin'): boolean => {
    if (!user) return false

    const roleHierarchy = {
      'student': 1,
      'teacher': 2,
      'admin': 3
    }

    return roleHierarchy[user.user_type] >= roleHierarchy[requiredRole]
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    hasPermission,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
