'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { AuthService } from '@/lib/auth'
import type { AuthUser } from '@/lib/auth'
import { BookOpen, Users, GraduationCap, Settings, FileText, Video, Headphones, Image, TestTube, LogOut, Upload, UserCheck } from 'lucide-react'

export default function Dashboard() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedStage, setSelectedStage] = useState<string | null>(null)
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser()
      if (!currentUser) {
        router.push('/auth')
        return
      }
      setUser(currentUser)
      setLoading(false)
    }
    checkAuth()
  }, [router])

  const handleSignOut = async () => {
    await AuthService.signOut()
    router.push('/')
  }

  const stages = [
    { id: 'primary', name: 'المرحلة الابتدائية', icon: BookOpen, color: 'bg-blue-500' },
    { id: 'middle', name: 'المرحلة الإعدادية', icon: Users, color: 'bg-green-500' },
    { id: 'secondary', name: 'المرحلة الثانوية', icon: GraduationCap, color: 'bg-purple-500' }
  ]

  const subjects = [
    { id: 'arabic', name: 'اللغة العربية', icon: FileText, color: 'bg-red-500' },
    { id: 'english', name: 'اللغة الإنجليزية', icon: BookOpen, color: 'bg-blue-500' },
    { id: 'math', name: 'الرياضيات', icon: Settings, color: 'bg-yellow-500' },
    { id: 'social', name: 'الدراسات الاجتماعية', icon: Users, color: 'bg-green-500' },
    { id: 'science', name: 'العلوم', icon: TestTube, color: 'bg-indigo-500' },
    { id: 'ict', name: 'الكمبيوتر (ICT)', icon: Settings, color: 'bg-gray-500' }
  ]

  const contentTypes = [
    { id: 'pdf', name: 'كتب PDF', icon: FileText, color: 'bg-red-500' },
    { id: 'video', name: 'فيديوهات تعليمية', icon: Video, color: 'bg-blue-500' },
    { id: 'audio', name: 'ملفات صوتية', icon: Headphones, color: 'bg-green-500' },
    { id: 'images', name: 'صور توضيحية', icon: Image, color: 'bg-yellow-500' },
    { id: 'tests', name: 'اختبارات تفاعلية', icon: TestTube, color: 'bg-purple-500' }
  ]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const getUserTypeLabel = (type: string) => {
    switch (type) {
      case 'student': return 'طالب'
      case 'teacher': return 'معلم'
      case 'admin': return 'إدارة'
      default: return type
    }
  }

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'student': return 'bg-blue-100 text-blue-800'
      case 'teacher': return 'bg-green-100 text-green-800'
      case 'admin': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-gray-800">
                مكتبة مدرسة White Land
              </h1>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getUserTypeColor(user.user_type)}`}>
                {getUserTypeLabel(user.user_type)}
              </span>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-gray-600">
                <UserCheck size={20} />
                <span>{user.full_name}</span>
              </div>
              
              {(user.user_type === 'teacher' || user.user_type === 'admin') && (
                <button
                  onClick={() => router.push('/content/upload')}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                >
                  <Upload size={16} />
                  رفع محتوى
                </button>
              )}
              
              <button 
                onClick={handleSignOut}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <LogOut size={16} />
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-6">
        {/* Navigation Breadcrumb */}
        <div className="flex items-center gap-2 mb-8 text-sm text-gray-600">
          <span 
            className="cursor-pointer hover:text-blue-600"
            onClick={() => {
              setSelectedStage(null)
              setSelectedSubject(null)
            }}
          >
            الرئيسية
          </span>
          {selectedStage && (
            <>
              <span>/</span>
              <span 
                className="cursor-pointer hover:text-blue-600"
                onClick={() => setSelectedSubject(null)}
              >
                {stages.find(s => s.id === selectedStage)?.name}
              </span>
            </>
          )}
          {selectedSubject && (
            <>
              <span>/</span>
              <span className="text-blue-600">
                {subjects.find(s => s.id === selectedSubject)?.name}
              </span>
            </>
          )}
        </div>

        {!selectedStage ? (
          /* Stages Selection */
          <div className="grid md:grid-cols-3 gap-6">
            {stages.map((stage) => {
              const Icon = stage.icon
              return (
                <div
                  key={stage.id}
                  className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                  onClick={() => setSelectedStage(stage.id)}
                >
                  <div className={`${stage.color} w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-center text-gray-800">
                    {stage.name}
                  </h3>
                </div>
              )
            })}
          </div>
        ) : !selectedSubject ? (
          /* Subjects Selection */
          <div className="grid md:grid-cols-3 gap-6">
            {subjects.map((subject) => {
              const Icon = subject.icon
              return (
                <div
                  key={subject.id}
                  className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                  onClick={() => setSelectedSubject(subject.id)}
                >
                  <div className={`${subject.color} w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-center text-gray-800">
                    {subject.name}
                  </h3>
                </div>
              )
            })}
          </div>
        ) : (
          /* Content Types */
          <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-6">
            {contentTypes.map((content) => {
              const Icon = content.icon
              return (
                <div
                  key={content.id}
                  className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                >
                  <div className={`${content.color} w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-sm font-semibold text-center text-gray-800">
                    {content.name}
                  </h4>
                </div>
              )
            })}
          </div>
        )}
      </main>
    </div>
  )
}
