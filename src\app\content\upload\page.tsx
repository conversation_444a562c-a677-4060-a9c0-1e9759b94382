'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AuthService } from '@/lib/auth'
import { ContentService } from '@/lib/content'
import FileUpload from '@/components/content/FileUpload'
import type { AuthUser, Stage, Subject } from '@/lib/supabase'
import { ArrowRight, BookOpen, Users, GraduationCap } from 'lucide-react'

export default function UploadPage() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [stages, setStages] = useState<Stage[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [selectedStage, setSelectedStage] = useState<string>('')
  const [selectedSubject, setSelectedSubject] = useState<string>('')
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser()
      if (!currentUser) {
        router.push('/auth')
        return
      }
      
      // Check if user has permission to upload
      if (currentUser.user_type === 'student') {
        router.push('/dashboard')
        return
      }

      setUser(currentUser)
      
      // Load stages
      try {
        const stagesData = await ContentService.getStages()
        setStages(stagesData)
      } catch (error) {
        console.error('Error loading stages:', error)
      }
      
      setLoading(false)
    }
    checkAuth()
  }, [router])

  useEffect(() => {
    if (selectedStage) {
      const loadSubjects = async () => {
        try {
          const subjectsData = await ContentService.getSubjectsByStage(selectedStage)
          setSubjects(subjectsData)
          setSelectedSubject('')
        } catch (error) {
          console.error('Error loading subjects:', error)
        }
      }
      loadSubjects()
    }
  }, [selectedStage])

  const handleFileUpload = async (file: File, metadata: any) => {
    if (!selectedSubject || !user) {
      throw new Error('يرجى اختيار المرحلة والمادة أولاً')
    }

    try {
      await ContentService.uploadContent(file, {
        ...metadata,
        subject_id: selectedSubject,
        uploaded_by: user.id,
      })
    } catch (error) {
      throw error
    }
  }

  const getStageIcon = (stageName: string) => {
    if (stageName.includes('ابتدائية')) return BookOpen
    if (stageName.includes('إعدادية')) return Users
    if (stageName.includes('ثانوية')) return GraduationCap
    return BookOpen
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-600 hover:text-gray-800"
            >
              <ArrowRight size={24} />
            </button>
            <h1 className="text-2xl font-bold text-gray-800">
              رفع محتوى جديد
            </h1>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto p-6">
        <div className="space-y-6">
          {/* Stage Selection */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">اختر المرحلة التعليمية</h3>
            <div className="grid md:grid-cols-3 gap-4">
              {stages.map((stage) => {
                const Icon = getStageIcon(stage.name)
                return (
                  <button
                    key={stage.id}
                    onClick={() => setSelectedStage(stage.id)}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      selectedStage === stage.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                    <p className="font-medium text-gray-800">{stage.name}</p>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Subject Selection */}
          {selectedStage && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">اختر المادة الدراسية</h3>
              <div className="grid md:grid-cols-3 gap-4">
                {subjects.map((subject) => (
                  <button
                    key={subject.id}
                    onClick={() => setSelectedSubject(subject.id)}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      selectedSubject === subject.id
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <p className="font-medium text-gray-800">{subject.name}</p>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* File Upload */}
          {selectedSubject && (
            <FileUpload
              onUpload={handleFileUpload}
              acceptedTypes={[
                'application/pdf',
                'video/mp4',
                'video/webm',
                'audio/mp3',
                'audio/wav',
                'audio/mpeg',
                'image/jpeg',
                'image/png',
                'image/gif'
              ]}
              maxSize={100} // 100MB
            />
          )}
        </div>
      </main>
    </div>
  )
}
