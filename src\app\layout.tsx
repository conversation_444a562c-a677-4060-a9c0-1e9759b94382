import type { Metadata } from 'next'
import './globals.css'
import { AuthProvider } from '@/components/auth/AuthProvider'

export const metadata: Metadata = {
  title: 'مكتبة مدرسة White Land الإلكترونية',
  description: 'المكتبة الإلكترونية التفاعلية لمدرسة White Land Language School',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
