import { supabase } from './supabase'
import type { Content, Stage, Subject } from './supabase'

export class ContentService {
  // Get all stages
  static async getStages(): Promise<Stage[]> {
    const { data, error } = await supabase
      .from('stages')
      .select('*')
      .order('order_index')

    if (error) throw error
    return data || []
  }

  // Get subjects by stage
  static async getSubjectsByStage(stageId: string): Promise<Subject[]> {
    const { data, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('stage_id', stageId)
      .order('order_index')

    if (error) throw error
    return data || []
  }

  // Get content by subject
  static async getContentBySubject(subjectId: string): Promise<Content[]> {
    const { data, error } = await supabase
      .from('content')
      .select(`
        *,
        uploaded_by_user:users!uploaded_by(full_name)
      `)
      .eq('subject_id', subjectId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get content by type and subject
  static async getContentByTypeAndSubject(subjectId: string, contentType: string): Promise<Content[]> {
    const { data, error } = await supabase
      .from('content')
      .select(`
        *,
        uploaded_by_user:users!uploaded_by(full_name)
      `)
      .eq('subject_id', subjectId)
      .eq('content_type', contentType)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Upload file to Supabase Storage
  static async uploadFile(file: File, path: string): Promise<string> {
    const { data, error } = await supabase.storage
      .from('content-files')
      .upload(path, file)

    if (error) throw error

    const { data: { publicUrl } } = supabase.storage
      .from('content-files')
      .getPublicUrl(path)

    return publicUrl
  }

  // Create content record
  static async createContent(contentData: {
    title: string
    description?: string
    content_type: 'pdf' | 'video' | 'audio' | 'image'
    file_url: string
    file_size: number
    subject_id: string
    uploaded_by: string
  }): Promise<Content> {
    const { data, error } = await supabase
      .from('content')
      .insert(contentData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Upload content (file + metadata)
  static async uploadContent(
    file: File,
    metadata: {
      title: string
      description?: string
      content_type: 'pdf' | 'video' | 'audio' | 'image'
      subject_id: string
      uploaded_by: string
    }
  ): Promise<Content> {
    try {
      // Generate unique file path
      const timestamp = Date.now()
      const fileExtension = file.name.split('.').pop()
      const fileName = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`
      const filePath = `${metadata.content_type}/${metadata.subject_id}/${fileName}`

      // Upload file to storage
      const fileUrl = await this.uploadFile(file, filePath)

      // Create content record
      const content = await this.createContent({
        ...metadata,
        file_url: fileUrl,
        file_size: file.size,
      })

      return content
    } catch (error) {
      throw error
    }
  }

  // Delete content
  static async deleteContent(contentId: string): Promise<void> {
    // First get the content to get the file URL
    const { data: content, error: fetchError } = await supabase
      .from('content')
      .select('file_url')
      .eq('id', contentId)
      .single()

    if (fetchError) throw fetchError

    // Extract file path from URL
    const url = new URL(content.file_url)
    const filePath = url.pathname.split('/').slice(-3).join('/')

    // Delete file from storage
    const { error: storageError } = await supabase.storage
      .from('content-files')
      .remove([filePath])

    if (storageError) console.warn('Failed to delete file from storage:', storageError)

    // Delete content record
    const { error: deleteError } = await supabase
      .from('content')
      .delete()
      .eq('id', contentId)

    if (deleteError) throw deleteError
  }

  // Update content metadata
  static async updateContent(contentId: string, updates: {
    title?: string
    description?: string
  }): Promise<Content> {
    const { data, error } = await supabase
      .from('content')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', contentId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Get content statistics
  static async getContentStats(subjectId?: string) {
    let query = supabase
      .from('content')
      .select('content_type')

    if (subjectId) {
      query = query.eq('subject_id', subjectId)
    }

    const { data, error } = await query

    if (error) throw error

    const stats = {
      total: data.length,
      pdf: data.filter(c => c.content_type === 'pdf').length,
      video: data.filter(c => c.content_type === 'video').length,
      audio: data.filter(c => c.content_type === 'audio').length,
      image: data.filter(c => c.content_type === 'image').length,
    }

    return stats
  }
}
