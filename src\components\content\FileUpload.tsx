'use client'

import { useState, useRef } from 'react'
import { Upload, X, FileText, Video, Headphones, Image as ImageIcon, CheckCircle } from 'lucide-react'

interface FileUploadProps {
  onUpload: (file: File, metadata: FileMetadata) => Promise<void>
  acceptedTypes: string[]
  maxSize?: number // in MB
}

interface FileMetadata {
  title: string
  description: string
  contentType: 'pdf' | 'video' | 'audio' | 'image'
}

export default function FileUpload({ onUpload, acceptedTypes, maxSize = 50 }: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [metadata, setMetadata] = useState<FileMetadata>({
    title: '',
    description: '',
    contentType: 'pdf'
  })
  const [uploading, setUploading] = useState(false)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [error, setError] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return FileText
    if (type.includes('video')) return Video
    if (type.includes('audio')) return Headphones
    if (type.includes('image')) return ImageIcon
    return FileText
  }

  const getContentType = (file: File): 'pdf' | 'video' | 'audio' | 'image' => {
    if (file.type.includes('pdf')) return 'pdf'
    if (file.type.includes('video')) return 'video'
    if (file.type.includes('audio')) return 'audio'
    if (file.type.includes('image')) return 'image'
    return 'pdf'
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`حجم الملف يجب أن يكون أقل من ${maxSize} ميجابايت`)
      return
    }

    // Check file type
    if (!acceptedTypes.some(type => file.type.includes(type))) {
      setError('نوع الملف غير مدعوم')
      return
    }

    setSelectedFile(file)
    setMetadata(prev => ({
      ...prev,
      contentType: getContentType(file),
      title: file.name.split('.')[0]
    }))
    setError('')
    setUploadSuccess(false)
  }

  const handleUpload = async () => {
    if (!selectedFile || !metadata.title.trim()) {
      setError('يرجى اختيار ملف وإدخال العنوان')
      return
    }

    setUploading(true)
    setError('')

    try {
      await onUpload(selectedFile, metadata)
      setUploadSuccess(true)
      setSelectedFile(null)
      setMetadata({
        title: '',
        description: '',
        contentType: 'pdf'
      })
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error: any) {
      setError(error.message || 'حدث خطأ أثناء رفع الملف')
    } finally {
      setUploading(false)
    }
  }

  const handleCancel = () => {
    setSelectedFile(null)
    setMetadata({
      title: '',
      description: '',
      contentType: 'pdf'
    })
    setError('')
    setUploadSuccess(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-6">رفع ملف جديد</h3>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
          {error}
        </div>
      )}

      {uploadSuccess && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4 flex items-center gap-2">
          <CheckCircle size={20} />
          تم رفع الملف بنجاح!
        </div>
      )}

      {!selectedFile ? (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-600 mb-4">اسحب الملف هنا أو انقر للاختيار</p>
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileSelect}
            accept={acceptedTypes.join(',')}
            className="hidden"
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            اختيار ملف
          </button>
          <p className="text-sm text-gray-500 mt-2">
            الحد الأقصى: {maxSize} ميجابايت
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {/* File Preview */}
          <div className="border border-gray-200 rounded-lg p-4 flex items-center gap-4">
            {(() => {
              const Icon = getFileIcon(selectedFile.type)
              return <Icon className="h-8 w-8 text-blue-600" />
            })()}
            <div className="flex-1">
              <p className="font-medium text-gray-800">{selectedFile.name}</p>
              <p className="text-sm text-gray-500">{formatFileSize(selectedFile.size)}</p>
            </div>
            <button
              onClick={handleCancel}
              className="text-red-600 hover:text-red-700"
            >
              <X size={20} />
            </button>
          </div>

          {/* Metadata Form */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                عنوان الملف *
              </label>
              <input
                type="text"
                value={metadata.title}
                onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل عنوان الملف"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وصف الملف
              </label>
              <textarea
                value={metadata.description}
                onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أدخل وصف الملف (اختياري)"
                rows={3}
              />
            </div>

            <div className="flex gap-4">
              <button
                onClick={handleUpload}
                disabled={uploading || !metadata.title.trim()}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                {uploading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <>
                    <Upload size={16} />
                    رفع الملف
                  </>
                )}
              </button>
              
              <button
                onClick={handleCancel}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
