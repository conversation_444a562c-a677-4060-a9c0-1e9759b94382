import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface User {
  id: string
  email: string
  full_name: string
  user_type: 'student' | 'teacher' | 'admin'
  created_at: string
  updated_at: string
}

export interface Stage {
  id: string
  name: string
  name_en: string
  order_index: number
  created_at: string
}

export interface Subject {
  id: string
  name: string
  name_en: string
  stage_id: string
  order_index: number
  created_at: string
}

export interface Content {
  id: string
  title: string
  description?: string
  content_type: 'pdf' | 'video' | 'audio' | 'image'
  file_url: string
  file_size?: number
  subject_id: string
  uploaded_by: string
  created_at: string
  updated_at: string
}

export interface Test {
  id: string
  title: string
  description?: string
  subject_id: string
  created_by: string
  total_questions: number
  time_limit?: number
  created_at: string
  updated_at: string
}

export interface Question {
  id: string
  test_id: string
  question_text: string
  question_type: 'multiple_choice' | 'true_false' | 'fill_blank'
  options?: string[]
  correct_answer: string
  points: number
  order_index: number
  created_at: string
}

export interface TestResult {
  id: string
  test_id: string
  student_id: string
  score: number
  total_points: number
  completed_at: string
  answers: Record<string, string>
}
