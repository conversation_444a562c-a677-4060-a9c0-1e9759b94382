'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import LoginForm from '@/components/auth/LoginForm'
import { AuthService } from '@/lib/auth'

export default function AuthPage() {
  const [mode, setMode] = useState<'login' | 'register'>('login')
  const router = useRouter()

  useEffect(() => {
    // Check if user is already logged in
    const checkAuth = async () => {
      const user = await AuthService.getCurrentUser()
      if (user) {
        router.push('/dashboard')
      }
    }
    checkAuth()
  }, [router])

  const handleSuccess = () => {
    router.push('/dashboard')
  }

  const toggleMode = () => {
    setMode(mode === 'login' ? 'register' : 'login')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            مكتبة مدرسة White Land
          </h1>
          <p className="text-gray-600">
            المكتبة الإلكترونية التفاعلية
          </p>
        </div>
        
        <LoginForm 
          mode={mode}
          onSuccess={handleSuccess}
          onToggleMode={toggleMode}
        />
      </div>
    </div>
  )
}
