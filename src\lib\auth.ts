import { supabase } from './supabase'
import type { User } from './supabase'

export interface AuthUser extends User {
  email: string
}

export class AuthService {
  // Sign up new user
  static async signUp(email: string, password: string, fullName: string, userType: 'student' | 'teacher' | 'admin' = 'student') {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            user_type: userType,
          }
        }
      })

      if (error) throw error

      // Insert user data into public.users table
      if (data.user) {
        const { error: insertError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email,
            full_name: fullName,
            user_type: userType,
          })

        if (insertError) throw insertError
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  // Sign in user
  static async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  // Sign out user
  static async signOut() {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { error: null }
    } catch (error) {
      return { error }
    }
  }

  // Get current user
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) return null

      const { data: userData, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) throw error

      return userData as AuthUser
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }

  // Check if user has permission
  static async hasPermission(requiredRole: 'student' | 'teacher' | 'admin'): Promise<boolean> {
    const user = await this.getCurrentUser()
    if (!user) return false

    const roleHierarchy = {
      'student': 1,
      'teacher': 2,
      'admin': 3
    }

    return roleHierarchy[user.user_type] >= roleHierarchy[requiredRole]
  }

  // Get user session
  static async getSession() {
    const { data: { session } } = await supabase.auth.getSession()
    return session
  }

  // Listen to auth changes
  static onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }
}
