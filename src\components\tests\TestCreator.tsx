'use client'

import { useState } from 'react'
import { TestService } from '@/lib/tests'
import { Plus, Trash2, Save, Clock } from 'lucide-react'

interface Question {
  id?: string
  question_text: string
  question_type: 'multiple_choice' | 'true_false' | 'fill_blank'
  options?: string[]
  correct_answer: string
  points: number
  order_index: number
}

interface TestCreatorProps {
  subjectId: string
  userId: string
  onTestCreated?: (testId: string) => void
}

export default function TestCreator({ subjectId, userId, onTestCreated }: TestCreatorProps) {
  const [testTitle, setTestTitle] = useState('')
  const [testDescription, setTestDescription] = useState('')
  const [timeLimit, setTimeLimit] = useState<number | undefined>(undefined)
  const [questions, setQuestions] = useState<Question[]>([])
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')

  const addQuestion = () => {
    const newQuestion: Question = {
      question_text: '',
      question_type: 'multiple_choice',
      options: ['', '', '', ''],
      correct_answer: '',
      points: 1,
      order_index: questions.length
    }
    setQuestions([...questions, newQuestion])
  }

  const updateQuestion = (index: number, updates: Partial<Question>) => {
    const updatedQuestions = [...questions]
    updatedQuestions[index] = { ...updatedQuestions[index], ...updates }
    setQuestions(updatedQuestions)
  }

  const removeQuestion = (index: number) => {
    const updatedQuestions = questions.filter((_, i) => i !== index)
    // Update order indices
    updatedQuestions.forEach((q, i) => q.order_index = i)
    setQuestions(updatedQuestions)
  }

  const updateQuestionOption = (questionIndex: number, optionIndex: number, value: string) => {
    const updatedQuestions = [...questions]
    if (updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options![optionIndex] = value
    }
    setQuestions(updatedQuestions)
  }

  const handleSave = async () => {
    if (!testTitle.trim()) {
      setError('يرجى إدخال عنوان الاختبار')
      return
    }

    if (questions.length === 0) {
      setError('يرجى إضافة سؤال واحد على الأقل')
      return
    }

    // Validate questions
    for (let i = 0; i < questions.length; i++) {
      const q = questions[i]
      if (!q.question_text.trim()) {
        setError(`يرجى إدخال نص السؤال رقم ${i + 1}`)
        return
      }
      if (!q.correct_answer.trim()) {
        setError(`يرجى إدخال الإجابة الصحيحة للسؤال رقم ${i + 1}`)
        return
      }
      if (q.question_type === 'multiple_choice' && (!q.options || q.options.some(opt => !opt.trim()))) {
        setError(`يرجى إدخال جميع الخيارات للسؤال رقم ${i + 1}`)
        return
      }
    }

    setSaving(true)
    setError('')

    try {
      // Create test
      const test = await TestService.createTest({
        title: testTitle,
        description: testDescription,
        subject_id: subjectId,
        created_by: userId,
        time_limit: timeLimit
      })

      // Add questions
      for (const question of questions) {
        await TestService.addQuestion({
          test_id: test.id,
          question_text: question.question_text,
          question_type: question.question_type,
          options: question.options,
          correct_answer: question.correct_answer,
          points: question.points,
          order_index: question.order_index
        })
      }

      // Reset form
      setTestTitle('')
      setTestDescription('')
      setTimeLimit(undefined)
      setQuestions([])

      onTestCreated?.(test.id)
    } catch (error: any) {
      setError(error.message || 'حدث خطأ أثناء حفظ الاختبار')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-6">إنشاء اختبار جديد</h3>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      <div className="space-y-6">
        {/* Test Info */}
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عنوان الاختبار *
            </label>
            <input
              type="text"
              value={testTitle}
              onChange={(e) => setTestTitle(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="أدخل عنوان الاختبار"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              مدة الاختبار (بالدقائق)
            </label>
            <div className="relative">
              <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="number"
                value={timeLimit || ''}
                onChange={(e) => setTimeLimit(e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="اختياري"
                min="1"
              />
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            وصف الاختبار
          </label>
          <textarea
            value={testDescription}
            onChange={(e) => setTestDescription(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="أدخل وصف الاختبار (اختياري)"
            rows={3}
          />
        </div>

        {/* Questions */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-800">الأسئلة</h4>
            <button
              onClick={addQuestion}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Plus size={16} />
              إضافة سؤال
            </button>
          </div>

          <div className="space-y-6">
            {questions.map((question, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h5 className="font-medium text-gray-800">السؤال {index + 1}</h5>
                  <button
                    onClick={() => removeQuestion(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نص السؤال *
                    </label>
                    <textarea
                      value={question.question_text}
                      onChange={(e) => updateQuestion(index, { question_text: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="أدخل نص السؤال"
                      rows={2}
                    />
                  </div>

                  <div className="grid md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نوع السؤال
                      </label>
                      <select
                        value={question.question_type}
                        onChange={(e) => updateQuestion(index, { 
                          question_type: e.target.value as any,
                          options: e.target.value === 'multiple_choice' ? ['', '', '', ''] : undefined
                        })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="multiple_choice">اختيار متعدد</option>
                        <option value="true_false">صح أو خطأ</option>
                        <option value="fill_blank">أكمل الفراغ</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        النقاط
                      </label>
                      <input
                        type="number"
                        value={question.points}
                        onChange={(e) => updateQuestion(index, { points: parseInt(e.target.value) || 1 })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        min="1"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الإجابة الصحيحة *
                      </label>
                      <input
                        type="text"
                        value={question.correct_answer}
                        onChange={(e) => updateQuestion(index, { correct_answer: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="الإجابة الصحيحة"
                      />
                    </div>
                  </div>

                  {question.question_type === 'multiple_choice' && question.options && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الخيارات
                      </label>
                      <div className="grid md:grid-cols-2 gap-2">
                        {question.options.map((option, optionIndex) => (
                          <input
                            key={optionIndex}
                            type="text"
                            value={option}
                            onChange={(e) => updateQuestionOption(index, optionIndex, e.target.value)}
                            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder={`الخيار ${optionIndex + 1}`}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            onClick={handleSave}
            disabled={saving || !testTitle.trim() || questions.length === 0}
            className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-semibold py-2 px-6 rounded-lg transition-colors flex items-center gap-2"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <>
                <Save size={16} />
                حفظ الاختبار
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
