import { supabase } from './supabase'
import type { Test, Question, TestResult } from './supabase'

export class TestService {
  // Create a new test
  static async createTest(testData: {
    title: string
    description?: string
    subject_id: string
    created_by: string
    time_limit?: number
  }): Promise<Test> {
    const { data, error } = await supabase
      .from('tests')
      .insert(testData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Get tests by subject
  static async getTestsBySubject(subjectId: string): Promise<Test[]> {
    const { data, error } = await supabase
      .from('tests')
      .select(`
        *,
        creator:users!created_by(full_name)
      `)
      .eq('subject_id', subjectId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get test by ID with questions
  static async getTestWithQuestions(testId: string): Promise<Test & { questions: Question[] }> {
    const { data: test, error: testError } = await supabase
      .from('tests')
      .select(`
        *,
        creator:users!created_by(full_name)
      `)
      .eq('id', testId)
      .single()

    if (testError) throw testError

    const { data: questions, error: questionsError } = await supabase
      .from('questions')
      .select('*')
      .eq('test_id', testId)
      .order('order_index')

    if (questionsError) throw questionsError

    return { ...test, questions: questions || [] }
  }

  // Add question to test
  static async addQuestion(questionData: {
    test_id: string
    question_text: string
    question_type: 'multiple_choice' | 'true_false' | 'fill_blank'
    options?: string[]
    correct_answer: string
    points: number
    order_index: number
  }): Promise<Question> {
    const { data, error } = await supabase
      .from('questions')
      .insert(questionData)
      .select()
      .single()

    if (error) throw error

    // Update test total_questions count
    await this.updateTestQuestionCount(questionData.test_id)

    return data
  }

  // Update question
  static async updateQuestion(questionId: string, updates: Partial<Question>): Promise<Question> {
    const { data, error } = await supabase
      .from('questions')
      .update(updates)
      .eq('id', questionId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Delete question
  static async deleteQuestion(questionId: string): Promise<void> {
    // Get test_id before deleting
    const { data: question, error: fetchError } = await supabase
      .from('questions')
      .select('test_id')
      .eq('id', questionId)
      .single()

    if (fetchError) throw fetchError

    const { error } = await supabase
      .from('questions')
      .delete()
      .eq('id', questionId)

    if (error) throw error

    // Update test total_questions count
    await this.updateTestQuestionCount(question.test_id)
  }

  // Update test question count
  private static async updateTestQuestionCount(testId: string): Promise<void> {
    const { data: questions, error } = await supabase
      .from('questions')
      .select('id')
      .eq('test_id', testId)

    if (error) throw error

    await supabase
      .from('tests')
      .update({ total_questions: questions?.length || 0 })
      .eq('id', testId)
  }

  // Submit test result
  static async submitTestResult(resultData: {
    test_id: string
    student_id: string
    answers: Record<string, string>
  }): Promise<TestResult> {
    // Get test with questions to calculate score
    const test = await this.getTestWithQuestions(resultData.test_id)
    
    let score = 0
    let totalPoints = 0

    test.questions.forEach(question => {
      totalPoints += question.points
      const studentAnswer = resultData.answers[question.id]
      
      if (studentAnswer && studentAnswer.toLowerCase().trim() === question.correct_answer.toLowerCase().trim()) {
        score += question.points
      }
    })

    const { data, error } = await supabase
      .from('test_results')
      .insert({
        ...resultData,
        score,
        total_points: totalPoints,
      })
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Get student test results
  static async getStudentResults(studentId: string, testId?: string): Promise<TestResult[]> {
    let query = supabase
      .from('test_results')
      .select(`
        *,
        test:tests(title, subject_id)
      `)
      .eq('student_id', studentId)
      .order('completed_at', { ascending: false })

    if (testId) {
      query = query.eq('test_id', testId)
    }

    const { data, error } = await query

    if (error) throw error
    return data || []
  }

  // Get test results for teachers/admins
  static async getTestResults(testId: string): Promise<TestResult[]> {
    const { data, error } = await supabase
      .from('test_results')
      .select(`
        *,
        student:users!student_id(full_name, email)
      `)
      .eq('test_id', testId)
      .order('completed_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Delete test
  static async deleteTest(testId: string): Promise<void> {
    const { error } = await supabase
      .from('tests')
      .delete()
      .eq('id', testId)

    if (error) throw error
  }

  // Update test
  static async updateTest(testId: string, updates: {
    title?: string
    description?: string
    time_limit?: number
  }): Promise<Test> {
    const { data, error } = await supabase
      .from('tests')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', testId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Get test statistics
  static async getTestStats(testId: string) {
    const { data: results, error } = await supabase
      .from('test_results')
      .select('score, total_points')
      .eq('test_id', testId)

    if (error) throw error

    if (!results || results.length === 0) {
      return {
        totalAttempts: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        passRate: 0
      }
    }

    const scores = results.map(r => (r.score / r.total_points) * 100)
    const passThreshold = 60 // 60% pass rate
    const passCount = scores.filter(score => score >= passThreshold).length

    return {
      totalAttempts: results.length,
      averageScore: scores.reduce((a, b) => a + b, 0) / scores.length,
      highestScore: Math.max(...scores),
      lowestScore: Math.min(...scores),
      passRate: (passCount / results.length) * 100
    }
  }
}
