<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبة مدرسة White Land الإلكترونية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            scroll-behavior: smooth;
        }

        /* Responsive toolbar adjustments */
        @media (max-width: 640px) {
            .toolbar-btn-text {
                display: none;
            }
            .toolbar-user-info {
                display: none;
            }
        }

        /* Ensure content doesn't hide behind fixed toolbar */
        .main-content {
            padding-top: 1rem;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="text-center mb-12 pt-12">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">
            مكتبة مدرسة White Land الإلكترونية
        </h1>
        <p class="text-lg text-gray-600">
            المكتبة التفاعلية الشاملة لجميع المراحل التعليمية
        </p>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto p-6">
        <!-- Stages Selection -->
        <div class="grid md:grid-cols-3 gap-6" id="stages">
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectStage('primary')">
                <div class="bg-blue-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    المرحلة الابتدائية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectStage('middle')">
                <div class="bg-green-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    المرحلة الإعدادية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectStage('secondary')">
                <div class="bg-purple-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    المرحلة الثانوية
                </h3>
            </div>
        </div>

        <!-- Subjects Selection (Hidden initially) -->
        <div class="grid md:grid-cols-3 gap-6 hidden" id="subjects">
            <!-- Library Section - Special Design -->
            <div class="bg-gradient-to-br from-amber-50 to-orange-100 rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105 border-2 border-amber-200" onclick="openLibrary()">
                <div class="bg-gradient-to-r from-amber-600 to-orange-600 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800 mb-2">
                    📚 المكتبة
                </h3>
                <p class="text-sm text-center text-amber-700">
                    مجموعة شاملة من الكتب والمراجع
                </p>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('arabic')">
                <div class="bg-red-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    اللغة العربية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('english')">
                <div class="bg-blue-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    اللغة الإنجليزية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('math')">
                <div class="bg-yellow-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    الرياضيات
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('social')">
                <div class="bg-green-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    الدراسات الاجتماعية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('science')">
                <div class="bg-indigo-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    العلوم
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('ict')">
                <div class="bg-gray-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    الكمبيوتر (ICT)
                </h3>
            </div>
        </div>

        <!-- Content Types (Hidden initially) -->
        <div class="grid md:grid-cols-3 lg:grid-cols-5 gap-6 hidden" id="content">
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" data-content-type="pdf">
                <div class="bg-red-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    كتب PDF
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" data-content-type="video">
                <div class="bg-blue-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    فيديوهات تعليمية
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" data-content-type="audio">
                <div class="bg-green-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    ملفات صوتية
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" data-content-type="image">
                <div class="bg-yellow-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    صور توضيحية
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" data-content-type="tests">
                <div class="bg-purple-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    اختبارات تفاعلية
                </h4>
            </div>
        </div>

        <!-- Library View (Hidden initially) -->
        <div class="hidden" id="libraryView">
            <!-- Library Header -->
            <div class="bg-gradient-to-r from-amber-600 to-orange-600 rounded-xl shadow-lg p-8 mb-8 text-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <div class="bg-white bg-opacity-20 p-4 rounded-full">
                            <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-3xl font-bold mb-2">📚 مكتبة المدرسة</h2>
                            <p class="text-amber-100" id="libraryStageTitle">مجموعة شاملة من الكتب والمراجع التعليمية</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="bg-white bg-opacity-20 rounded-lg p-4">
                            <div class="text-2xl font-bold" id="totalBooks">0</div>
                            <div class="text-sm text-amber-100">كتاب متاح</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Library Search and Filters -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text" id="librarySearch" placeholder="ابحث عن كتاب..." class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                            <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <select id="subjectFilter" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                            <option value="">جميع المواد</option>
                            <option value="arabic">اللغة العربية</option>
                            <option value="english">اللغة الإنجليزية</option>
                            <option value="math">الرياضيات</option>
                            <option value="social">الدراسات الاجتماعية</option>
                            <option value="science">العلوم</option>
                            <option value="ict">الكمبيوتر</option>
                        </select>
                        <select id="sortFilter" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                            <option value="newest">الأحدث</option>
                            <option value="oldest">الأقدم</option>
                            <option value="title">العنوان</option>
                            <option value="author">المؤلف</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Library Shelves -->
            <div class="grid gap-8" id="libraryShelves">
                <!-- Shelves will be populated here -->
            </div>

            <!-- Add Book Button is now in the toolbar -->
        </div>

        <!-- Breadcrumb Navigation -->
        <div class="flex items-center gap-2 mb-8 text-sm text-gray-600" id="breadcrumb">
            <span class="cursor-pointer hover:text-blue-600" onclick="goHome()">الرئيسية</span>
        </div>
    </main>

    <!-- Fixed Toolbar -->
    <div class="fixed top-0 left-0 right-0 bg-white shadow-lg border-b border-gray-200 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo/Title -->
                <div class="flex items-center gap-3">
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h1 class="text-xl font-bold text-gray-800">مدرسة White Land</h1>
                </div>

                <!-- Toolbar Actions -->
                <div class="flex items-center gap-3">
                    <!-- Add Book Button (for teachers/admins) -->
                    <button id="addBookBtn" onclick="showAddBookModal()" class="hidden bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span class="toolbar-btn-text">إضافة كتاب</span>
                    </button>

                    <!-- Upload Content Button (for teachers/admins) -->
                    <button id="uploadBtn" onclick="showUploadModal()" class="hidden bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <span class="toolbar-btn-text">رفع محتوى</span>
                    </button>

                    <!-- Analytics Button (for teachers/admins) -->
                    <button id="analyticsBtn" onclick="showAnalytics()" class="hidden bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span class="toolbar-btn-text">تحليلات</span>
                    </button>

                    <!-- User Info / Login Button -->
                    <div id="userSection">
                        <button onclick="showLogin()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            <span class="hidden sm:inline">تسجيل الدخول</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Spacer -->
    <div class="h-16"></div>

    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center" id="modalTitle">تسجيل الدخول</h2>

            <!-- Error Message -->
            <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4"></div>

            <!-- Success Message -->
            <div id="successMessage" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4"></div>

            <form id="authForm" class="space-y-4">
                <!-- Full Name (for registration only) -->
                <div id="fullNameField" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                    <input type="text" id="fullName" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل اسمك الكامل">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" id="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل بريدك الإلكتروني" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                    <input type="password" id="password" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل كلمة المرور" required>
                </div>

                <!-- User Type (for registration only) -->
                <div id="userTypeField" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع المستخدم</label>
                    <select id="userType" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="student">طالب</option>
                        <option value="teacher">معلم</option>
                        <option value="admin">إدارة</option>
                    </select>
                </div>

                <button type="submit" id="submitBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">
                    تسجيل الدخول
                </button>
            </form>

            <div class="mt-4 text-center space-y-2">
                <button id="toggleMode" onclick="toggleAuthMode()" class="text-blue-600 hover:text-blue-700 font-medium">
                    ليس لديك حساب؟ أنشئ حساباً جديداً
                </button>
                <br>
                <button onclick="hideLogin()" class="text-gray-600 hover:text-gray-800">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- Content Modal -->
    <div id="contentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800" id="contentTitle">المحتوى</h2>
                <button onclick="hideContentModal()" class="text-gray-600 hover:text-gray-800">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div id="contentBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Upload Modal (for teachers/admins) -->
    <div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">رفع محتوى جديد</h2>
                <button onclick="hideUploadModal()" class="text-gray-600 hover:text-gray-800">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="uploadForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">عنوان المحتوى</label>
                    <input type="text" id="contentTitleInput" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل عنوان المحتوى" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف المحتوى</label>
                    <textarea id="contentDescription" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل وصف المحتوى" rows="3"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع المحتوى</label>
                    <select id="contentTypeSelect" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                        <option value="">اختر نوع المحتوى</option>
                        <option value="pdf">📄 كتاب PDF</option>
                        <option value="powerpoint">📊 عرض تقديمي (PowerPoint)</option>
                        <option value="video">🎥 فيديو تعليمي</option>
                        <option value="audio">🎵 ملف صوتي</option>
                        <option value="image">🖼️ صورة توضيحية</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الملف أو الرابط</label>
                    <input type="file" id="fileInput"
                           accept=".pdf,.ppt,.pptx,.mp4,.webm,.mp3,.wav,.jpg,.jpeg,.png,.gif"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-2">
                    <p class="text-xs text-gray-500 mb-2">
                        الملفات المدعومة: PDF, PowerPoint (.ppt, .pptx), فيديو (.mp4, .webm), صوت (.mp3, .wav), صور (.jpg, .png, .gif)
                    </p>
                    <p class="text-sm text-gray-500 mb-2 font-medium">أو</p>
                    <input type="url" id="urlInput" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل رابط المحتوى (YouTube، Google Drive، OneDrive، إلخ)">
                    <p class="text-xs text-gray-500 mt-1">
                        مثال: https://www.youtube.com/watch?v=... أو https://drive.google.com/file/d/...
                    </p>
                </div>

                <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">
                    رفع المحتوى
                </button>
            </form>
        </div>
    </div>

    <!-- Add Book Modal -->
    <div id="addBookModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
                    <svg class="w-8 h-8 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    إضافة كتاب جديد للمكتبة
                </h2>
                <button onclick="hideAddBookModal()" class="text-gray-600 hover:text-gray-800">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="addBookForm" class="space-y-6">
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الكتاب</label>
                        <input type="text" id="bookTitle" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent" placeholder="أدخل عنوان الكتاب" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المؤلف</label>
                        <input type="text" id="bookAuthor" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent" placeholder="اسم المؤلف">
                    </div>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المادة الدراسية</label>
                        <select id="bookSubject" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent" required>
                            <option value="">اختر المادة</option>
                            <option value="arabic">اللغة العربية</option>
                            <option value="english">اللغة الإنجليزية</option>
                            <option value="math">الرياضيات</option>
                            <option value="social">الدراسات الاجتماعية</option>
                            <option value="science">العلوم</option>
                            <option value="ict">الكمبيوتر</option>
                            <option value="general">عام</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">سنة النشر</label>
                        <input type="number" id="bookYear" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent" placeholder="2024" min="1900" max="2030">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف الكتاب</label>
                    <textarea id="bookDescription" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent" placeholder="وصف مختصر عن محتوى الكتاب" rows="3"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملف الكتاب أو الرابط</label>
                    <input type="file" id="bookFile" accept=".pdf" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent mb-2">
                    <p class="text-xs text-gray-500 mb-2">أو</p>
                    <input type="url" id="bookUrl" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent" placeholder="رابط الكتاب (Google Drive، إلخ)">
                </div>

                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-amber-600 hover:bg-amber-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
                        إضافة الكتاب
                    </button>
                    <button type="button" onclick="hideAddBookModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Audio Explanation Modal -->
    <div id="addAudioModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
                    <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                    </svg>
                    إضافة شرح صوتي للكتاب
                </h2>
                <button onclick="hideAddAudioModal()" class="text-gray-600 hover:text-gray-800">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <h3 class="font-semibold text-orange-800 mb-2" id="audioBookTitle">عنوان الكتاب</h3>
                <p class="text-sm text-orange-600">أضف شرح صوتي لمساعدة الطلاب على فهم محتوى الكتاب بشكل أفضل</p>
            </div>

            <form id="addAudioForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الشرح الصوتي</label>
                    <input type="text" id="audioTitle" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="مثال: شرح الفصل الأول" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف الشرح</label>
                    <textarea id="audioDescription" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="وصف مختصر عن محتوى الشرح الصوتي" rows="3"></textarea>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">مدة الشرح (بالدقائق)</label>
                        <input type="number" id="audioDuration" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="15" min="1">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الشرح</label>
                        <select id="audioType" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <option value="chapter">شرح فصل</option>
                            <option value="summary">ملخص</option>
                            <option value="exercise">حل تمارين</option>
                            <option value="review">مراجعة</option>
                            <option value="introduction">مقدمة</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الملف الصوتي أو الرابط</label>
                    <input type="file" id="audioFile" accept=".mp3,.wav,.ogg,.m4a" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent mb-2">
                    <p class="text-xs text-gray-500 mb-2">
                        الملفات المدعومة: MP3, WAV, OGG, M4A (أقل من 50 ميجابايت)
                    </p>
                    <p class="text-sm text-gray-500 mb-2 font-medium">أو</p>
                    <input type="url" id="audioUrl" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="رابط الملف الصوتي (Google Drive، SoundCloud، إلخ)">
                </div>

                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
                        إضافة الشرح الصوتي
                    </button>
                    <button type="button" onclick="hideAddAudioModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Audio Player Modal -->
    <div id="audioPlayerModal" class="fixed inset-0 bg-black bg-opacity-75 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 max-w-2xl w-full mx-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-800 flex items-center gap-2" id="audioPlayerTitle">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                    </svg>
                    شرح صوتي
                </h2>
                <button onclick="hideAudioPlayerModal()" class="text-gray-600 hover:text-gray-800">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-6 mb-6">
                <h3 class="font-semibold text-gray-800 mb-2" id="audioExplanationTitle">عنوان الشرح</h3>
                <p class="text-sm text-gray-600 mb-4" id="audioExplanationDescription">وصف الشرح</p>

                <!-- Audio Player -->
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <audio id="audioPlayer" controls class="w-full">
                        <source id="audioSource" src="" type="audio/mpeg">
                        متصفحك لا يدعم تشغيل الملفات الصوتية.
                    </audio>
                </div>

                <div class="flex items-center justify-between mt-4 text-sm text-gray-600">
                    <span id="audioInfo">معلومات الملف</span>
                    <div class="flex gap-4">
                        <button onclick="downloadCurrentAudio()" class="text-orange-600 hover:text-orange-700 flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            تحميل
                        </button>
                        <button onclick="shareCurrentAudio()" class="text-orange-600 hover:text-orange-700 flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                            مشاركة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Analytics Modal -->
    <div id="analyticsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg max-w-7xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-xl">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        تحليلات نشاط الطلاب
                    </h2>
                    <button onclick="hideAnalytics()" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Filter Controls -->
                <div class="flex flex-wrap gap-4 mt-4">
                    <select id="stageFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                        <option value="">جميع المراحل</option>
                        <option value="primary">المرحلة الابتدائية</option>
                        <option value="middle">المرحلة الإعدادية</option>
                        <option value="secondary">المرحلة الثانوية</option>
                    </select>

                    <select id="subjectAnalyticsFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                        <option value="">جميع المواد</option>
                        <option value="arabic">اللغة العربية</option>
                        <option value="english">اللغة الإنجليزية</option>
                        <option value="math">الرياضيات</option>
                        <option value="social">الدراسات الاجتماعية</option>
                        <option value="science">العلوم</option>
                        <option value="ict">الكمبيوتر</option>
                    </select>

                    <select id="timeFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                        <option value="all">كل الأوقات</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>

                    <button onclick="refreshAnalytics()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                        تحديث البيانات
                    </button>
                </div>
            </div>

            <div class="p-6">
                <!-- Summary Cards -->
                <div class="grid md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">إجمالي الطلاب</p>
                                <p class="text-2xl font-bold" id="totalStudents">0</p>
                            </div>
                            <svg class="w-8 h-8 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">الكتب المقروءة</p>
                                <p class="text-2xl font-bold" id="totalBooksRead">0</p>
                            </div>
                            <svg class="w-8 h-8 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-100 text-sm">الاختبارات المكتملة</p>
                                <p class="text-2xl font-bold" id="totalTestsCompleted">0</p>
                            </div>
                            <svg class="w-8 h-8 text-yellow-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">متوسط الدرجات</p>
                                <p class="text-2xl font-bold" id="averageScore">0%</p>
                            </div>
                            <svg class="w-8 h-8 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="border-b border-gray-200 mb-6">
                    <nav class="flex space-x-8">
                        <button onclick="switchAnalyticsTab('students')" class="analytics-tab active py-2 px-1 border-b-2 border-purple-500 font-medium text-sm text-purple-600">
                            نشاط الطلاب
                        </button>
                        <button onclick="switchAnalyticsTab('books')" class="analytics-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                            إحصائيات الكتب
                        </button>
                        <button onclick="switchAnalyticsTab('tests')" class="analytics-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                            نتائج الاختبارات
                        </button>
                        <button onclick="switchAnalyticsTab('engagement')" class="analytics-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                            مستوى التفاعل
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div id="studentsTab" class="analytics-tab-content">
                    <div class="bg-white rounded-lg border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">نشاط الطلاب التفصيلي</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكتب المقروءة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاختبارات</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط الدرجات</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر نشاط</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مستوى التفاعل</th>
                                    </tr>
                                </thead>
                                <tbody id="studentsTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Student data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div id="booksTab" class="analytics-tab-content hidden">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">أكثر الكتب قراءة</h3>
                            <div id="popularBooks" class="space-y-3">
                                <!-- Popular books will be populated here -->
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">إحصائيات المواد</h3>
                            <div id="subjectStats" class="space-y-3">
                                <!-- Subject statistics will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <div id="testsTab" class="analytics-tab-content hidden">
                    <div class="bg-white rounded-lg border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">نتائج الاختبارات</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاختبار</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد المشاركين</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط الدرجات</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">أعلى درجة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">أقل درجة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">معدل النجاح</th>
                                    </tr>
                                </thead>
                                <tbody id="testsTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Test results will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div id="engagementTab" class="analytics-tab-content hidden">
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">الطلاب الأكثر نشاطاً</h3>
                            <div id="topStudents" class="space-y-3">
                                <!-- Top students will be populated here -->
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">النشاط اليومي</h3>
                            <div id="dailyActivity" class="space-y-3">
                                <!-- Daily activity will be populated here -->
                            </div>
                        </div>

                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">مؤشرات الأداء</h3>
                            <div id="performanceIndicators" class="space-y-3">
                                <!-- Performance indicators will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentStage = null;
        let currentSubject = null;
        let currentUser = null;
        let isLoginMode = true;
        let contentData = {};
        let libraryData = {};
        let isInLibrary = false;
        let currentAudioBookId = null;
        let currentAudioData = null;
        let studentActivities = {};
        let currentAnalyticsTab = 'students';

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadUserFromStorage();
            initializeContent();
            initializeLibrary();
            initializeStudentActivities();
            updateUI();
        });

        // User management
        function loadUserFromStorage() {
            const userData = localStorage.getItem('currentUser');
            if (userData) {
                currentUser = JSON.parse(userData);
                updateUI();
            }
        }

        function saveUserToStorage(user) {
            localStorage.setItem('currentUser', JSON.stringify(user));
            currentUser = user;
        }

        function logout() {
            localStorage.removeItem('currentUser');
            currentUser = null;
            updateUI();
            goHome();
        }

        // Navigation functions
        function selectStage(stage) {
            currentStage = stage;
            currentSubject = null;
            isInLibrary = false;

            document.getElementById('stages').classList.add('hidden');
            document.getElementById('subjects').classList.remove('hidden');
            document.getElementById('content').classList.add('hidden');
            document.getElementById('libraryView').classList.add('hidden');

            updateBreadcrumb();
        }

        function selectSubject(subject) {
            currentSubject = subject;

            document.getElementById('subjects').classList.add('hidden');
            document.getElementById('content').classList.remove('hidden');

            updateBreadcrumb();
        }

        function goHome() {
            currentStage = null;
            currentSubject = null;
            isInLibrary = false;

            document.getElementById('stages').classList.remove('hidden');
            document.getElementById('subjects').classList.add('hidden');
            document.getElementById('content').classList.add('hidden');
            document.getElementById('libraryView').classList.add('hidden');

            updateBreadcrumb();
        }

        // Library functions
        function openLibrary() {
            if (!currentStage) return;

            isInLibrary = true;

            document.getElementById('subjects').classList.add('hidden');
            document.getElementById('content').classList.add('hidden');
            document.getElementById('libraryView').classList.remove('hidden');

            // Update library title
            const stageNames = {
                'primary': 'المرحلة الابتدائية',
                'middle': 'المرحلة الإعدادية',
                'secondary': 'المرحلة الثانوية'
            };
            document.getElementById('libraryStageTitle').textContent = `مكتبة ${stageNames[currentStage]}`;

            // Add book button is now in the toolbar
            // This section is no longer needed

            loadLibraryBooks();
            updateBreadcrumb();
        }

        function initializeLibrary() {
            if (!localStorage.getItem('libraryData')) {
                const sampleLibrary = {
                    'primary': [
                        {
                            id: 1,
                            title: 'كتاب القراءة والكتابة للصف الأول',
                            author: 'وزارة التربية والتعليم',
                            subject: 'arabic',
                            year: 2024,
                            description: 'كتاب تعليم القراءة والكتابة للمبتدئين مع التمارين التفاعلية',
                            url: 'https://drive.google.com/file/d/sample1/view',
                            fileSize: 2048000,
                            addedBy: 'المعلمة فاطمة',
                            addedAt: '2024-01-15',
                            downloads: 45,
                            rating: 4.8,
                            audioExplanations: [
                                {
                                    id: 1,
                                    title: 'شرح الحروف الأبجدية',
                                    description: 'شرح مفصل للحروف العربية مع النطق الصحيح',
                                    url: 'https://drive.google.com/file/d/audio1/view',
                                    duration: 15,
                                    type: 'chapter',
                                    addedBy: 'المعلمة فاطمة',
                                    addedAt: '2024-01-20'
                                },
                                {
                                    id: 2,
                                    title: 'تمارين الكتابة',
                                    description: 'شرح كيفية كتابة الحروف بالطريقة الصحيحة',
                                    url: 'https://drive.google.com/file/d/audio2/view',
                                    duration: 12,
                                    type: 'exercise',
                                    addedBy: 'المعلم أحمد',
                                    addedAt: '2024-01-25'
                                }
                            ]
                        },
                        {
                            id: 2,
                            title: 'أساسيات الرياضيات للأطفال',
                            author: 'د. أحمد محمد',
                            subject: 'math',
                            year: 2023,
                            description: 'تعليم الأرقام والعمليات الحسابية البسيطة بطريقة ممتعة',
                            url: 'https://drive.google.com/file/d/sample2/view',
                            fileSize: 1536000,
                            addedBy: 'المعلم خالد',
                            addedAt: '2024-01-20',
                            downloads: 32,
                            rating: 4.6,
                            audioExplanations: [
                                {
                                    id: 3,
                                    title: 'تعلم الأرقام من 1 إلى 10',
                                    description: 'شرح الأرقام مع أمثلة تطبيقية',
                                    url: 'https://drive.google.com/file/d/audio3/view',
                                    duration: 18,
                                    type: 'chapter',
                                    addedBy: 'المعلم خالد',
                                    addedAt: '2024-01-22'
                                }
                            ]
                        },
                        {
                            id: 3,
                            title: 'قصص الأطفال التعليمية',
                            author: 'مجموعة مؤلفين',
                            subject: 'arabic',
                            year: 2024,
                            description: 'مجموعة من القصص الهادفة لتنمية مهارات القراءة',
                            url: 'https://drive.google.com/file/d/sample3/view',
                            fileSize: 3072000,
                            addedBy: 'المعلمة سارة',
                            addedAt: '2024-02-01',
                            downloads: 67,
                            rating: 4.9
                        }
                    ],
                    'middle': [
                        {
                            id: 4,
                            title: 'الجبر والهندسة للصف الثاني الإعدادي',
                            author: 'د. محمد علي',
                            subject: 'math',
                            year: 2024,
                            description: 'شرح مفصل لأساسيات الجبر والهندسة مع أمثلة محلولة',
                            url: 'https://drive.google.com/file/d/sample4/view',
                            fileSize: 4096000,
                            addedBy: 'المعلم أحمد',
                            addedAt: '2024-02-10',
                            downloads: 89,
                            rating: 4.7
                        },
                        {
                            id: 5,
                            title: 'تاريخ مصر القديمة',
                            author: 'د. ليلى حسن',
                            subject: 'social',
                            year: 2023,
                            description: 'رحلة عبر تاريخ مصر الفرعونية والحضارة المصرية القديمة',
                            url: 'https://drive.google.com/file/d/sample5/view',
                            fileSize: 2560000,
                            addedBy: 'المعلمة نور',
                            addedAt: '2024-02-15',
                            downloads: 54,
                            rating: 4.5
                        }
                    ],
                    'secondary': [
                        {
                            id: 6,
                            title: 'الفيزياء الحديثة',
                            author: 'د. عمر الشريف',
                            subject: 'science',
                            year: 2024,
                            description: 'مبادئ الفيزياء الحديثة والنظريات العلمية المعاصرة',
                            url: 'https://drive.google.com/file/d/sample6/view',
                            fileSize: 5120000,
                            addedBy: 'المعلم محمود',
                            addedAt: '2024-03-01',
                            downloads: 76,
                            rating: 4.8
                        },
                        {
                            id: 7,
                            title: 'الأدب العربي الحديث',
                            author: 'د. فاطمة الزهراء',
                            subject: 'arabic',
                            year: 2023,
                            description: 'دراسة شاملة للأدب العربي في العصر الحديث',
                            url: 'https://drive.google.com/file/d/sample7/view',
                            fileSize: 3584000,
                            addedBy: 'المعلمة ليلى',
                            addedAt: '2024-03-05',
                            downloads: 43,
                            rating: 4.6
                        }
                    ]
                };
                localStorage.setItem('libraryData', JSON.stringify(sampleLibrary));
            }
            libraryData = JSON.parse(localStorage.getItem('libraryData'));
        }

        // Student Activity Tracking System
        function initializeStudentActivities() {
            if (!localStorage.getItem('studentActivities')) {
                const sampleActivities = {
                    // Sample student activities for demonstration
                    'student1': {
                        userId: 'student1',
                        userName: 'أحمد محمد',
                        userEmail: '<EMAIL>',
                        booksRead: [
                            {
                                bookId: 1,
                                bookTitle: 'كتاب القراءة والكتابة للصف الأول',
                                stage: 'primary',
                                subject: 'arabic',
                                readAt: '2024-03-01T10:30:00Z',
                                timeSpent: 25, // minutes
                                completed: true
                            },
                            {
                                bookId: 2,
                                bookTitle: 'أساسيات الرياضيات للأطفال',
                                stage: 'primary',
                                subject: 'math',
                                readAt: '2024-03-02T14:15:00Z',
                                timeSpent: 18,
                                completed: false
                            }
                        ],
                        testsCompleted: [
                            {
                                testId: 1,
                                testTitle: 'اختبار اللغة العربية - الصف الأول',
                                stage: 'primary',
                                subject: 'arabic',
                                completedAt: '2024-03-03T09:45:00Z',
                                score: 8,
                                totalQuestions: 10,
                                percentage: 80,
                                timeSpent: 12
                            }
                        ],
                        audioListened: [
                            {
                                audioId: 1,
                                audioTitle: 'شرح الحروف الأبجدية',
                                bookId: 1,
                                listenedAt: '2024-03-01T11:00:00Z',
                                duration: 15,
                                completed: true
                            }
                        ],
                        lastActivity: '2024-03-03T09:45:00Z',
                        totalTimeSpent: 70, // total minutes
                        engagementScore: 85
                    },
                    'student2': {
                        userId: 'student2',
                        userName: 'فاطمة علي',
                        userEmail: '<EMAIL>',
                        booksRead: [
                            {
                                bookId: 1,
                                bookTitle: 'كتاب القراءة والكتابة للصف الأول',
                                stage: 'primary',
                                subject: 'arabic',
                                readAt: '2024-03-02T16:20:00Z',
                                timeSpent: 30,
                                completed: true
                            }
                        ],
                        testsCompleted: [
                            {
                                testId: 1,
                                testTitle: 'اختبار اللغة العربية - الصف الأول',
                                stage: 'primary',
                                subject: 'arabic',
                                completedAt: '2024-03-02T17:30:00Z',
                                score: 9,
                                totalQuestions: 10,
                                percentage: 90,
                                timeSpent: 10
                            }
                        ],
                        audioListened: [],
                        lastActivity: '2024-03-02T17:30:00Z',
                        totalTimeSpent: 40,
                        engagementScore: 92
                    },
                    'student3': {
                        userId: 'student3',
                        userName: 'محمد حسن',
                        userEmail: '<EMAIL>',
                        booksRead: [
                            {
                                bookId: 2,
                                bookTitle: 'أساسيات الرياضيات للأطفال',
                                stage: 'primary',
                                subject: 'math',
                                readAt: '2024-03-01T13:45:00Z',
                                timeSpent: 22,
                                completed: true
                            }
                        ],
                        testsCompleted: [],
                        audioListened: [
                            {
                                audioId: 3,
                                audioTitle: 'تعلم الأرقام من 1 إلى 10',
                                bookId: 2,
                                listenedAt: '2024-03-01T14:10:00Z',
                                duration: 18,
                                completed: true
                            }
                        ],
                        lastActivity: '2024-03-01T14:10:00Z',
                        totalTimeSpent: 40,
                        engagementScore: 75
                    }
                };
                localStorage.setItem('studentActivities', JSON.stringify(sampleActivities));
            }
            studentActivities = JSON.parse(localStorage.getItem('studentActivities'));
        }

        // Track student activities
        function trackBookRead(bookId, bookTitle, stage, subject) {
            if (!currentUser || currentUser.userType !== 'student') return;

            if (!studentActivities[currentUser.id]) {
                studentActivities[currentUser.id] = {
                    userId: currentUser.id,
                    userName: currentUser.fullName,
                    userEmail: currentUser.email,
                    booksRead: [],
                    testsCompleted: [],
                    audioListened: [],
                    lastActivity: new Date().toISOString(),
                    totalTimeSpent: 0,
                    engagementScore: 0
                };
            }

            const activity = studentActivities[currentUser.id];
            const existingBook = activity.booksRead.find(b => b.bookId === bookId);

            if (!existingBook) {
                activity.booksRead.push({
                    bookId,
                    bookTitle,
                    stage,
                    subject,
                    readAt: new Date().toISOString(),
                    timeSpent: Math.floor(Math.random() * 30) + 10, // Random time 10-40 minutes
                    completed: Math.random() > 0.3 // 70% completion rate
                });

                activity.lastActivity = new Date().toISOString();
                activity.totalTimeSpent += Math.floor(Math.random() * 30) + 10;
                updateEngagementScore(currentUser.id);

                localStorage.setItem('studentActivities', JSON.stringify(studentActivities));
            }
        }

        function trackTestCompleted(testId, testTitle, stage, subject, score, totalQuestions, timeSpent) {
            if (!currentUser || currentUser.userType !== 'student') return;

            if (!studentActivities[currentUser.id]) {
                initializeStudentActivity(currentUser.id);
            }

            const activity = studentActivities[currentUser.id];
            const percentage = Math.round((score / totalQuestions) * 100);

            activity.testsCompleted.push({
                testId,
                testTitle,
                stage,
                subject,
                completedAt: new Date().toISOString(),
                score,
                totalQuestions,
                percentage,
                timeSpent
            });

            activity.lastActivity = new Date().toISOString();
            activity.totalTimeSpent += timeSpent;
            updateEngagementScore(currentUser.id);

            localStorage.setItem('studentActivities', JSON.stringify(studentActivities));
        }

        function trackAudioListened(audioId, audioTitle, bookId, duration) {
            if (!currentUser || currentUser.userType !== 'student') return;

            if (!studentActivities[currentUser.id]) {
                initializeStudentActivity(currentUser.id);
            }

            const activity = studentActivities[currentUser.id];

            activity.audioListened.push({
                audioId,
                audioTitle,
                bookId,
                listenedAt: new Date().toISOString(),
                duration,
                completed: Math.random() > 0.2 // 80% completion rate
            });

            activity.lastActivity = new Date().toISOString();
            activity.totalTimeSpent += duration;
            updateEngagementScore(currentUser.id);

            localStorage.setItem('studentActivities', JSON.stringify(studentActivities));
        }

        function initializeStudentActivity(userId) {
            studentActivities[userId] = {
                userId: userId,
                userName: currentUser.fullName,
                userEmail: currentUser.email,
                booksRead: [],
                testsCompleted: [],
                audioListened: [],
                lastActivity: new Date().toISOString(),
                totalTimeSpent: 0,
                engagementScore: 0
            };
        }

        function updateEngagementScore(userId) {
            const activity = studentActivities[userId];
            if (!activity) return;

            let score = 0;

            // Books read (40% weight)
            score += Math.min(activity.booksRead.length * 10, 40);

            // Tests completed (35% weight)
            if (activity.testsCompleted.length > 0) {
                const avgScore = activity.testsCompleted.reduce((sum, test) => sum + test.percentage, 0) / activity.testsCompleted.length;
                score += (avgScore / 100) * 35;
            }

            // Audio listened (15% weight)
            score += Math.min(activity.audioListened.length * 5, 15);

            // Recent activity (10% weight)
            const daysSinceLastActivity = (new Date() - new Date(activity.lastActivity)) / (1000 * 60 * 60 * 24);
            if (daysSinceLastActivity < 1) score += 10;
            else if (daysSinceLastActivity < 7) score += 5;

            activity.engagementScore = Math.min(Math.round(score), 100);
        }

        // Analytics Functions
        function showAnalytics() {
            if (!currentUser || (currentUser.userType !== 'teacher' && currentUser.userType !== 'admin')) {
                showNotification('غير مصرح لك بعرض التحليلات', 'error');
                return;
            }

            document.getElementById('analyticsModal').classList.remove('hidden');
            document.getElementById('analyticsModal').classList.add('flex');

            loadAnalyticsData();
        }

        function hideAnalytics() {
            document.getElementById('analyticsModal').classList.add('hidden');
            document.getElementById('analyticsModal').classList.remove('flex');
        }

        function switchAnalyticsTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.analytics-tab').forEach(tab => {
                tab.classList.remove('active', 'border-purple-500', 'text-purple-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            event.target.classList.add('active', 'border-purple-500', 'text-purple-600');
            event.target.classList.remove('border-transparent', 'text-gray-500');

            // Update tab content
            document.querySelectorAll('.analytics-tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            currentAnalyticsTab = tabName;

            loadAnalyticsData();
        }

        function loadAnalyticsData() {
            const stageFilter = document.getElementById('stageFilter')?.value || '';
            const subjectFilter = document.getElementById('subjectAnalyticsFilter')?.value || '';
            const timeFilter = document.getElementById('timeFilter')?.value || 'all';

            // Filter activities based on filters
            const filteredActivities = Object.values(studentActivities).filter(activity => {
                if (stageFilter) {
                    const hasStageActivity = activity.booksRead.some(book => book.stage === stageFilter) ||
                                          activity.testsCompleted.some(test => test.stage === stageFilter);
                    if (!hasStageActivity) return false;
                }

                if (subjectFilter) {
                    const hasSubjectActivity = activity.booksRead.some(book => book.subject === subjectFilter) ||
                                             activity.testsCompleted.some(test => test.subject === subjectFilter);
                    if (!hasSubjectActivity) return false;
                }

                if (timeFilter !== 'all') {
                    const activityDate = new Date(activity.lastActivity);
                    const now = new Date();
                    const diffDays = (now - activityDate) / (1000 * 60 * 60 * 24);

                    if (timeFilter === 'today' && diffDays > 1) return false;
                    if (timeFilter === 'week' && diffDays > 7) return false;
                    if (timeFilter === 'month' && diffDays > 30) return false;
                }

                return true;
            });

            updateSummaryCards(filteredActivities);

            switch(currentAnalyticsTab) {
                case 'students':
                    loadStudentsData(filteredActivities);
                    break;
                case 'books':
                    loadBooksData(filteredActivities);
                    break;
                case 'tests':
                    loadTestsData(filteredActivities);
                    break;
                case 'engagement':
                    loadEngagementData(filteredActivities);
                    break;
            }
        }

        function updateSummaryCards(activities) {
            const totalStudents = activities.length;
            const totalBooksRead = activities.reduce((sum, activity) => sum + activity.booksRead.length, 0);
            const totalTestsCompleted = activities.reduce((sum, activity) => sum + activity.testsCompleted.length, 0);

            let totalScore = 0;
            let totalTests = 0;
            activities.forEach(activity => {
                activity.testsCompleted.forEach(test => {
                    totalScore += test.percentage;
                    totalTests++;
                });
            });

            const averageScore = totalTests > 0 ? Math.round(totalScore / totalTests) : 0;

            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('totalBooksRead').textContent = totalBooksRead;
            document.getElementById('totalTestsCompleted').textContent = totalTestsCompleted;
            document.getElementById('averageScore').textContent = averageScore + '%';
        }

        function loadStudentsData(activities) {
            const tbody = document.getElementById('studentsTableBody');

            if (activities.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            لا توجد بيانات طلاب متاحة
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = activities.map(activity => {
                const avgScore = activity.testsCompleted.length > 0
                    ? Math.round(activity.testsCompleted.reduce((sum, test) => sum + test.percentage, 0) / activity.testsCompleted.length)
                    : 0;

                const lastActivity = new Date(activity.lastActivity).toLocaleDateString('ar-EG');

                const engagementColor = activity.engagementScore >= 80 ? 'text-green-600' :
                                      activity.engagementScore >= 60 ? 'text-yellow-600' : 'text-red-600';

                const engagementLabel = activity.engagementScore >= 80 ? 'ممتاز' :
                                      activity.engagementScore >= 60 ? 'جيد' : 'يحتاج تحسين';

                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">${activity.userName.charAt(0)}</span>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900">${activity.userName}</div>
                                    <div class="text-sm text-gray-500">${activity.userEmail}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${activity.booksRead.length} كتاب
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${activity.testsCompleted.length} اختبار
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${avgScore}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${lastActivity}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${engagementColor}">
                                ${engagementLabel} (${activity.engagementScore}%)
                            </span>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function loadBooksData(activities) {
            // Popular books analysis
            const bookStats = {};
            activities.forEach(activity => {
                activity.booksRead.forEach(book => {
                    if (!bookStats[book.bookId]) {
                        bookStats[book.bookId] = {
                            title: book.bookTitle,
                            subject: book.subject,
                            stage: book.stage,
                            readCount: 0,
                            totalTime: 0
                        };
                    }
                    bookStats[book.bookId].readCount++;
                    bookStats[book.bookId].totalTime += book.timeSpent;
                });
            });

            const popularBooks = Object.values(bookStats)
                .sort((a, b) => b.readCount - a.readCount)
                .slice(0, 5);

            const popularBooksContainer = document.getElementById('popularBooks');
            popularBooksContainer.innerHTML = popularBooks.map((book, index) => `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-3">
                        <span class="flex items-center justify-center w-6 h-6 bg-purple-100 text-purple-600 rounded-full text-sm font-bold">
                            ${index + 1}
                        </span>
                        <div>
                            <p class="font-medium text-gray-900 text-sm">${book.title}</p>
                            <p class="text-xs text-gray-500">${getSubjectName(book.subject)} - ${getStageName(book.stage)}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-semibold text-gray-900">${book.readCount} قراءة</p>
                        <p class="text-xs text-gray-500">${book.totalTime} دقيقة</p>
                    </div>
                </div>
            `).join('');

            // Subject statistics
            const subjectStats = {};
            activities.forEach(activity => {
                activity.booksRead.forEach(book => {
                    if (!subjectStats[book.subject]) {
                        subjectStats[book.subject] = { books: 0, time: 0 };
                    }
                    subjectStats[book.subject].books++;
                    subjectStats[book.subject].time += book.timeSpent;
                });
            });

            const subjectStatsContainer = document.getElementById('subjectStats');
            subjectStatsContainer.innerHTML = Object.entries(subjectStats).map(([subject, stats]) => `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <p class="font-medium text-gray-900">${getSubjectName(subject)}</p>
                        <p class="text-sm text-gray-500">${stats.books} كتاب مقروء</p>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-purple-600">${stats.time}</p>
                        <p class="text-xs text-gray-500">دقيقة</p>
                    </div>
                </div>
            `).join('');
        }

        function loadTestsData(activities) {
            const testStats = {};
            activities.forEach(activity => {
                activity.testsCompleted.forEach(test => {
                    if (!testStats[test.testId]) {
                        testStats[test.testId] = {
                            title: test.testTitle,
                            subject: test.subject,
                            stage: test.stage,
                            participants: 0,
                            totalScore: 0,
                            scores: []
                        };
                    }
                    testStats[test.testId].participants++;
                    testStats[test.testId].totalScore += test.percentage;
                    testStats[test.testId].scores.push(test.percentage);
                });
            });

            const tbody = document.getElementById('testsTableBody');

            if (Object.keys(testStats).length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            لا توجد نتائج اختبارات متاحة
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = Object.values(testStats).map(test => {
                const avgScore = Math.round(test.totalScore / test.participants);
                const maxScore = Math.max(...test.scores);
                const minScore = Math.min(...test.scores);
                const passRate = Math.round((test.scores.filter(score => score >= 60).length / test.scores.length) * 100);

                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${test.title}</div>
                            <div class="text-sm text-gray-500">${getSubjectName(test.subject)} - ${getStageName(test.stage)}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${test.participants} طالب
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${avgScore}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                            ${maxScore}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                            ${minScore}%
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${passRate}%
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function loadEngagementData(activities) {
            // Top students
            const topStudents = activities
                .sort((a, b) => b.engagementScore - a.engagementScore)
                .slice(0, 5);

            const topStudentsContainer = document.getElementById('topStudents');
            topStudentsContainer.innerHTML = topStudents.map((student, index) => `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-3">
                        <span class="flex items-center justify-center w-6 h-6 bg-yellow-100 text-yellow-600 rounded-full text-sm font-bold">
                            ${index + 1}
                        </span>
                        <div>
                            <p class="font-medium text-gray-900 text-sm">${student.userName}</p>
                            <p class="text-xs text-gray-500">${student.booksRead.length} كتاب، ${student.testsCompleted.length} اختبار</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-semibold text-green-600">${student.engagementScore}%</p>
                    </div>
                </div>
            `).join('');

            // Daily activity (simplified)
            const dailyActivityContainer = document.getElementById('dailyActivity');
            const today = new Date();
            const dailyData = [];

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                const dayActivity = activities.filter(activity => {
                    const activityDate = new Date(activity.lastActivity).toISOString().split('T')[0];
                    return activityDate === dateStr;
                }).length;

                dailyData.push({
                    date: date.toLocaleDateString('ar-EG', { weekday: 'short' }),
                    activity: dayActivity
                });
            }

            dailyActivityContainer.innerHTML = dailyData.map(day => `
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span class="text-sm text-gray-600">${day.date}</span>
                    <span class="text-sm font-semibold text-purple-600">${day.activity} طالب</span>
                </div>
            `).join('');

            // Performance indicators
            const totalTime = activities.reduce((sum, activity) => sum + activity.totalTimeSpent, 0);
            const avgTime = activities.length > 0 ? Math.round(totalTime / activities.length) : 0;
            const activeStudents = activities.filter(activity => {
                const daysSinceLastActivity = (new Date() - new Date(activity.lastActivity)) / (1000 * 60 * 60 * 24);
                return daysSinceLastActivity <= 7;
            }).length;

            const performanceContainer = document.getElementById('performanceIndicators');
            performanceContainer.innerHTML = `
                <div class="p-3 bg-blue-50 rounded-lg">
                    <p class="text-sm text-blue-600 font-medium">متوسط وقت الدراسة</p>
                    <p class="text-lg font-bold text-blue-800">${avgTime} دقيقة</p>
                </div>
                <div class="p-3 bg-green-50 rounded-lg">
                    <p class="text-sm text-green-600 font-medium">الطلاب النشطون</p>
                    <p class="text-lg font-bold text-green-800">${activeStudents} طالب</p>
                </div>
                <div class="p-3 bg-purple-50 rounded-lg">
                    <p class="text-sm text-purple-600 font-medium">إجمالي ساعات الدراسة</p>
                    <p class="text-lg font-bold text-purple-800">${Math.round(totalTime / 60)} ساعة</p>
                </div>
            `;
        }

        function getSubjectName(subject) {
            const names = {
                'arabic': 'اللغة العربية',
                'english': 'اللغة الإنجليزية',
                'math': 'الرياضيات',
                'social': 'الدراسات الاجتماعية',
                'science': 'العلوم',
                'ict': 'الكمبيوتر'
            };
            return names[subject] || subject;
        }

        function getStageName(stage) {
            const names = {
                'primary': 'ابتدائية',
                'middle': 'إعدادية',
                'secondary': 'ثانوية'
            };
            return names[stage] || stage;
        }

        function refreshAnalytics() {
            loadAnalyticsData();
            showNotification('تم تحديث البيانات بنجاح 📊', 'success');
        }

        function loadLibraryBooks() {
            const books = libraryData[currentStage] || [];
            const searchTerm = document.getElementById('librarySearch')?.value.toLowerCase() || '';
            const subjectFilter = document.getElementById('subjectFilter')?.value || '';
            const sortFilter = document.getElementById('sortFilter')?.value || 'newest';

            // Filter books
            let filteredBooks = books.filter(book => {
                const matchesSearch = book.title.toLowerCase().includes(searchTerm) ||
                                    book.author.toLowerCase().includes(searchTerm) ||
                                    book.description.toLowerCase().includes(searchTerm);
                const matchesSubject = !subjectFilter || book.subject === subjectFilter;
                return matchesSearch && matchesSubject;
            });

            // Sort books
            filteredBooks.sort((a, b) => {
                switch(sortFilter) {
                    case 'oldest':
                        return new Date(a.addedAt) - new Date(b.addedAt);
                    case 'title':
                        return a.title.localeCompare(b.title, 'ar');
                    case 'author':
                        return a.author.localeCompare(b.author, 'ar');
                    case 'newest':
                    default:
                        return new Date(b.addedAt) - new Date(a.addedAt);
                }
            });

            // Update total books count
            document.getElementById('totalBooks').textContent = filteredBooks.length;

            // Group books by subject for shelf display
            const booksBySubject = {};
            filteredBooks.forEach(book => {
                if (!booksBySubject[book.subject]) {
                    booksBySubject[book.subject] = [];
                }
                booksBySubject[book.subject].push(book);
            });

            displayLibraryShelves(booksBySubject);
        }

        function displayLibraryShelves(booksBySubject) {
            const container = document.getElementById('libraryShelves');

            if (Object.keys(booksBySubject).length === 0) {
                container.innerHTML = `
                    <div class="text-center py-16">
                        <div class="text-gray-400 mb-4">
                            <svg class="w-24 h-24 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-600 mb-2">لا توجد كتب متاحة</h3>
                        <p class="text-gray-500 mb-6">لم يتم إضافة أي كتب في هذا القسم بعد</p>
                        ${currentUser && (currentUser.userType === 'teacher' || currentUser.userType === 'admin') ?
                            '<button onclick="showAddBookModal()" class="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg">إضافة أول كتاب</button>' :
                            ''
                        }
                    </div>
                `;
                return;
            }

            const subjectNames = {
                'arabic': 'اللغة العربية',
                'english': 'اللغة الإنجليزية',
                'math': 'الرياضيات',
                'social': 'الدراسات الاجتماعية',
                'science': 'العلوم',
                'ict': 'الكمبيوتر',
                'general': 'كتب عامة'
            };

            const subjectColors = {
                'arabic': 'from-red-500 to-red-600',
                'english': 'from-blue-500 to-blue-600',
                'math': 'from-yellow-500 to-yellow-600',
                'social': 'from-green-500 to-green-600',
                'science': 'from-indigo-500 to-indigo-600',
                'ict': 'from-gray-500 to-gray-600',
                'general': 'from-purple-500 to-purple-600'
            };

            let html = '';

            Object.keys(booksBySubject).forEach(subject => {
                const books = booksBySubject[subject];
                const subjectName = subjectNames[subject] || subject;
                const colorClass = subjectColors[subject] || 'from-gray-500 to-gray-600';

                html += `
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <!-- Shelf Header -->
                        <div class="bg-gradient-to-r ${colorClass} p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
                                    </svg>
                                    <h3 class="text-2xl font-bold">${subjectName}</h3>
                                </div>
                                <div class="bg-white bg-opacity-20 rounded-lg px-3 py-1">
                                    <span class="font-semibold">${books.length} كتاب</span>
                                </div>
                            </div>
                        </div>

                        <!-- Books Shelf -->
                        <div class="p-6">
                            <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                `;

                books.forEach(book => {
                    html += createBookCard(book);
                });

                html += `
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function createBookCard(book) {
            const stars = '★'.repeat(Math.floor(book.rating)) + '☆'.repeat(5 - Math.floor(book.rating));
            const hasAudio = book.audioExplanations && book.audioExplanations.length > 0;

            return `
                <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg border border-amber-200 p-4 hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                    <!-- Book Cover -->
                    <div class="bg-gradient-to-br from-amber-600 to-orange-600 rounded-lg p-4 mb-4 text-white text-center min-h-[120px] flex flex-col justify-center shadow-lg relative">
                        ${hasAudio ? `
                            <div class="absolute top-2 right-2 bg-green-500 rounded-full p-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                                </svg>
                            </div>
                        ` : ''}
                        <svg class="w-8 h-8 mx-auto mb-2 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h4 class="font-bold text-sm leading-tight">${book.title}</h4>
                    </div>

                    <!-- Book Info -->
                    <div class="space-y-2">
                        <div>
                            <p class="font-semibold text-gray-800 text-sm">${book.author}</p>
                            <p class="text-xs text-gray-600">${book.year || 'غير محدد'}</p>
                        </div>

                        <p class="text-xs text-gray-600 line-clamp-2">${book.description}</p>

                        <!-- Rating and Stats -->
                        <div class="flex items-center justify-between text-xs">
                            <div class="text-yellow-500">${stars}</div>
                            <div class="text-gray-500">📥 ${book.downloads || 0}</div>
                        </div>

                        <!-- File Size -->
                        ${book.fileSize ? `<div class="text-xs text-gray-500">📁 ${formatFileSize(book.fileSize)}</div>` : ''}
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-4 space-y-2">
                        <div class="flex gap-2">
                            <button onclick="openBookContent('${book.url}', '${book.title}')" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-3 rounded-lg flex items-center justify-center gap-1 transition-colors">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                قراءة
                            </button>
                            <button onclick="downloadBook('${book.url}', '${book.title}', ${book.id})" class="flex-1 bg-green-600 hover:bg-green-700 text-white text-xs py-2 px-3 rounded-lg flex items-center justify-center gap-1 transition-colors">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                تحميل
                            </button>
                        </div>

                        <!-- Audio Explanation Section -->
                        ${hasAudio ? `
                            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="text-xs font-semibold text-green-800 flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                                        </svg>
                                        شرح صوتي متاح
                                    </h5>
                                    <span class="text-xs text-green-600">${book.audioExplanations.length} ملف</span>
                                </div>
                                <div class="space-y-1">
                                    ${book.audioExplanations.map((audio, index) => `
                                        <button onclick="playAudioExplanation(${book.id}, ${index})" class="w-full text-left bg-white hover:bg-green-100 border border-green-200 rounded px-2 py-1 text-xs text-green-700 transition-colors flex items-center gap-2">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15a2 2 0 002-2V9a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293L10.293 4.293A1 1 0 009.586 4H8a2 2 0 00-2 2v5a2 2 0 002 2z"></path>
                                            </svg>
                                            ${audio.title}
                                        </button>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}

                        <!-- Add Audio Button for Teachers/Admins -->
                        ${currentUser && (currentUser.userType === 'teacher' || currentUser.userType === 'admin') ? `
                            <button onclick="showAddAudioModal(${book.id})" class="w-full bg-orange-600 hover:bg-orange-700 text-white text-xs py-2 px-3 rounded-lg flex items-center justify-center gap-1 transition-colors">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                إضافة شرح صوتي
                            </button>
                        ` : ''}

                        <div class="flex gap-2">
                            <button onclick="shareBook(${book.id})" class="flex-1 bg-amber-600 hover:bg-amber-700 text-white text-xs py-2 px-3 rounded-lg flex items-center justify-center gap-1 transition-colors">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                مشاركة
                            </button>
                            <button onclick="addToFavorites(${book.id})" class="flex-1 bg-pink-600 hover:bg-pink-700 text-white text-xs py-2 px-3 rounded-lg flex items-center justify-center gap-1 transition-colors">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                مفضلة
                            </button>
                        </div>

                        ${currentUser && (currentUser.userType === 'admin' || book.addedBy === currentUser.fullName) ?
                            `<button onclick="deleteBook(${book.id})" class="w-full bg-red-600 hover:bg-red-700 text-white text-xs py-2 px-3 rounded-lg flex items-center justify-center gap-1 transition-colors">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                حذف الكتاب
                            </button>` :
                            ''
                        }
                    </div>

                    <!-- Added by info -->
                    <div class="mt-3 pt-3 border-t border-amber-200">
                        <p class="text-xs text-gray-500">أضيف بواسطة: ${book.addedBy}</p>
                        <p class="text-xs text-gray-400">${new Date(book.addedAt).toLocaleDateString('ar-EG')}</p>
                    </div>
                </div>
            `;
        }

        // Book interaction functions
        function openBookContent(url, title) {
            // Track book reading activity
            if (currentUser && currentUser.userType === 'student') {
                // Find book details for tracking
                const allBooks = [];
                Object.keys(libraryData).forEach(stage => {
                    libraryData[stage].forEach(book => {
                        if (book.title === title) {
                            trackBookRead(book.id, book.title, stage, book.subject);
                        }
                    });
                });
            }

            if (url.includes('drive.google.com')) {
                // Convert Google Drive view link to preview link
                const fileId = extractGoogleDriveFileId(url);
                if (fileId) {
                    const previewUrl = `https://drive.google.com/file/d/${fileId}/preview`;
                    window.open(previewUrl, '_blank');
                } else {
                    window.open(url, '_blank');
                }
            } else {
                window.open(url, '_blank');
            }
        }

        function downloadBook(url, title, bookId) {
            // Increment download count
            const books = libraryData[currentStage] || [];
            const book = books.find(b => b.id === bookId);
            if (book) {
                book.downloads = (book.downloads || 0) + 1;
                localStorage.setItem('libraryData', JSON.stringify(libraryData));
                loadLibraryBooks(); // Refresh display
            }

            downloadContent(url, title, 'pdf');
            showNotification(`تم بدء تحميل "${title}" 📚`, 'success');
        }

        function shareBook(bookId) {
            const books = libraryData[currentStage] || [];
            const book = books.find(b => b.id === bookId);
            if (!book) return;

            const shareText = `📚 ${book.title}\n👨‍🏫 ${book.author}\n📖 ${book.description}\n\n🔗 مكتبة مدرسة White Land الإلكترونية`;

            if (navigator.share) {
                navigator.share({
                    title: book.title,
                    text: shareText,
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(shareText).then(() => {
                    showNotification('تم نسخ معلومات الكتاب للحافظة 📋', 'success');
                });
            }
        }

        function addToFavorites(bookId) {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول لإضافة الكتب للمفضلة', 'warning');
                return;
            }

            const favorites = JSON.parse(localStorage.getItem('userFavorites') || '{}');
            if (!favorites[currentUser.id]) {
                favorites[currentUser.id] = [];
            }

            if (favorites[currentUser.id].includes(bookId)) {
                showNotification('الكتاب موجود بالفعل في المفضلة ❤️', 'info');
            } else {
                favorites[currentUser.id].push(bookId);
                localStorage.setItem('userFavorites', JSON.stringify(favorites));
                showNotification('تم إضافة الكتاب للمفضلة ❤️', 'success');
            }
        }

        function deleteBook(bookId) {
            if (!confirm('هل أنت متأكد من حذف هذا الكتاب من المكتبة؟')) return;

            const books = libraryData[currentStage] || [];
            libraryData[currentStage] = books.filter(book => book.id !== bookId);
            localStorage.setItem('libraryData', JSON.stringify(libraryData));

            showNotification('تم حذف الكتاب من المكتبة 🗑️', 'success');
            loadLibraryBooks();
        }

        // Add book modal functions
        function showAddBookModal() {
            document.getElementById('addBookModal').classList.remove('hidden');
            document.getElementById('addBookModal').classList.add('flex');
        }

        function hideAddBookModal() {
            document.getElementById('addBookModal').classList.add('hidden');
            document.getElementById('addBookModal').classList.remove('flex');
            document.getElementById('addBookForm').reset();
        }

        // Audio explanation functions
        function showAddAudioModal(bookId) {
            currentAudioBookId = bookId;
            const books = libraryData[currentStage] || [];
            const book = books.find(b => b.id === bookId);

            if (book) {
                document.getElementById('audioBookTitle').textContent = book.title;
            }

            document.getElementById('addAudioModal').classList.remove('hidden');
            document.getElementById('addAudioModal').classList.add('flex');
        }

        function hideAddAudioModal() {
            document.getElementById('addAudioModal').classList.add('hidden');
            document.getElementById('addAudioModal').classList.remove('flex');
            document.getElementById('addAudioForm').reset();
            currentAudioBookId = null;
        }

        function playAudioExplanation(bookId, audioIndex) {
            const books = libraryData[currentStage] || [];
            const book = books.find(b => b.id === bookId);

            if (!book || !book.audioExplanations || !book.audioExplanations[audioIndex]) {
                showNotification('لم يتم العثور على الملف الصوتي', 'error');
                return;
            }

            const audio = book.audioExplanations[audioIndex];
            currentAudioData = { book, audio };

            // Track audio listening activity
            if (currentUser && currentUser.userType === 'student') {
                trackAudioListened(audio.id, audio.title, bookId, audio.duration || 0);
            }

            // Update modal content
            document.getElementById('audioPlayerTitle').textContent = `${book.title} - ${audio.title}`;
            document.getElementById('audioExplanationTitle').textContent = audio.title;
            document.getElementById('audioExplanationDescription').textContent = audio.description;
            document.getElementById('audioInfo').textContent = `المدة: ${audio.duration} دقيقة • النوع: ${getAudioTypeLabel(audio.type)}`;

            // Set audio source
            const audioPlayer = document.getElementById('audioPlayer');
            const audioSource = document.getElementById('audioSource');

            if (audio.url.includes('drive.google.com')) {
                const fileId = extractGoogleDriveFileId(audio.url);
                if (fileId) {
                    audioSource.src = `https://drive.google.com/uc?export=download&id=${fileId}`;
                } else {
                    audioSource.src = audio.url;
                }
            } else {
                audioSource.src = audio.url;
            }

            audioPlayer.load();

            // Show modal
            document.getElementById('audioPlayerModal').classList.remove('hidden');
            document.getElementById('audioPlayerModal').classList.add('flex');
        }

        function hideAudioPlayerModal() {
            document.getElementById('audioPlayerModal').classList.add('hidden');
            document.getElementById('audioPlayerModal').classList.remove('flex');

            // Stop audio
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.pause();
            audioPlayer.currentTime = 0;

            currentAudioData = null;
        }

        function getAudioTypeLabel(type) {
            const labels = {
                'chapter': 'شرح فصل',
                'summary': 'ملخص',
                'exercise': 'حل تمارين',
                'review': 'مراجعة',
                'introduction': 'مقدمة'
            };
            return labels[type] || type;
        }

        function downloadCurrentAudio() {
            if (!currentAudioData) return;

            const { book, audio } = currentAudioData;
            downloadContent(audio.url, `${book.title} - ${audio.title}`, 'audio');
            showNotification(`تم بدء تحميل "${audio.title}" 🎵`, 'success');
        }

        function shareCurrentAudio() {
            if (!currentAudioData) return;

            const { book, audio } = currentAudioData;
            const shareText = `🎵 ${audio.title}\n📚 من كتاب: ${book.title}\n👨‍🏫 ${book.author}\n⏱️ المدة: ${audio.duration} دقيقة\n\n📖 ${audio.description}\n\n🔗 مكتبة مدرسة White Land الإلكترونية`;

            if (navigator.share) {
                navigator.share({
                    title: audio.title,
                    text: shareText,
                    url: window.location.href
                });
            } else {
                navigator.clipboard.writeText(shareText).then(() => {
                    showNotification('تم نسخ معلومات الشرح الصوتي للحافظة 📋', 'success');
                });
            }
        }

        // Handle add book form submission
        document.getElementById('addBookForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const title = document.getElementById('bookTitle').value;
            const author = document.getElementById('bookAuthor').value;
            const subject = document.getElementById('bookSubject').value;
            const year = document.getElementById('bookYear').value;
            const description = document.getElementById('bookDescription').value;
            const file = document.getElementById('bookFile').files[0];
            const url = document.getElementById('bookUrl').value;

            if (!title || !subject) {
                showNotification('يرجى إدخال عنوان الكتاب والمادة الدراسية', 'warning');
                return;
            }

            if (!file && !url) {
                showNotification('يرجى اختيار ملف أو إدخال رابط الكتاب', 'warning');
                return;
            }

            if (!libraryData[currentStage]) {
                libraryData[currentStage] = [];
            }

            const newBook = {
                id: Date.now(),
                title,
                author: author || 'غير محدد',
                subject,
                year: year ? parseInt(year) : null,
                description: description || 'لا يوجد وصف',
                url: url || (file ? URL.createObjectURL(file) : '#'),
                fileSize: file ? file.size : null,
                addedBy: currentUser.fullName,
                addedAt: new Date().toISOString(),
                downloads: 0,
                rating: 4.5
            };

            libraryData[currentStage].push(newBook);
            localStorage.setItem('libraryData', JSON.stringify(libraryData));

            showNotification(`تم إضافة الكتاب "${title}" للمكتبة بنجاح! 📚`, 'success');
            hideAddBookModal();
            loadLibraryBooks();
        });

        // Handle add audio form submission
        document.getElementById('addAudioForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (!currentAudioBookId) {
                showNotification('خطأ في تحديد الكتاب', 'error');
                return;
            }

            const title = document.getElementById('audioTitle').value;
            const description = document.getElementById('audioDescription').value;
            const duration = document.getElementById('audioDuration').value;
            const type = document.getElementById('audioType').value;
            const file = document.getElementById('audioFile').files[0];
            const url = document.getElementById('audioUrl').value;

            if (!title.trim()) {
                showNotification('يرجى إدخال عنوان الشرح الصوتي', 'warning');
                return;
            }

            if (!file && !url) {
                showNotification('يرجى اختيار ملف صوتي أو إدخال رابط', 'warning');
                return;
            }

            // Validate audio file type
            if (file) {
                const validTypes = ['.mp3', '.wav', '.ogg', '.m4a'];
                const fileName = file.name.toLowerCase();
                const fileExtension = fileName.substring(fileName.lastIndexOf('.'));

                if (!validTypes.includes(fileExtension)) {
                    showNotification(`نوع الملف غير مدعوم. الأنواع المدعومة: ${validTypes.join(', ')}`, 'error');
                    return;
                }

                if (file.size > 50 * 1024 * 1024) { // 50MB limit
                    showNotification('حجم الملف كبير جداً. يرجى استخدام ملف أصغر من 50 ميجابايت.', 'warning');
                    return;
                }
            }

            // Find the book and add audio explanation
            const books = libraryData[currentStage] || [];
            const bookIndex = books.findIndex(b => b.id === currentAudioBookId);

            if (bookIndex === -1) {
                showNotification('لم يتم العثور على الكتاب', 'error');
                return;
            }

            if (!books[bookIndex].audioExplanations) {
                books[bookIndex].audioExplanations = [];
            }

            const newAudio = {
                id: Date.now(),
                title,
                description: description || 'لا يوجد وصف',
                url: url || (file ? URL.createObjectURL(file) : '#'),
                duration: duration ? parseInt(duration) : null,
                type,
                addedBy: currentUser.fullName,
                addedAt: new Date().toISOString()
            };

            books[bookIndex].audioExplanations.push(newAudio);
            localStorage.setItem('libraryData', JSON.stringify(libraryData));

            showNotification(`تم إضافة الشرح الصوتي "${title}" بنجاح! 🎵`, 'success');
            hideAddAudioModal();
            loadLibraryBooks();
        });

        // Search and filter event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners after DOM is loaded
            setTimeout(() => {
                const searchInput = document.getElementById('librarySearch');
                const subjectFilter = document.getElementById('subjectFilter');
                const sortFilter = document.getElementById('sortFilter');

                if (searchInput) {
                    searchInput.addEventListener('input', () => {
                        if (isInLibrary) loadLibraryBooks();
                    });
                }

                if (subjectFilter) {
                    subjectFilter.addEventListener('change', () => {
                        if (isInLibrary) loadLibraryBooks();
                    });
                }

                if (sortFilter) {
                    sortFilter.addEventListener('change', () => {
                        if (isInLibrary) loadLibraryBooks();
                    });
                }
            }, 1000);
        });

        function updateBreadcrumb() {
            const breadcrumb = document.getElementById('breadcrumb');
            let html = '<span class="cursor-pointer hover:text-blue-600" onclick="goHome()">الرئيسية</span>';

            if (currentStage) {
                const stageNames = {
                    'primary': 'المرحلة الابتدائية',
                    'middle': 'المرحلة الإعدادية',
                    'secondary': 'المرحلة الثانوية'
                };
                html += '<span>/</span><span class="cursor-pointer hover:text-blue-600" onclick="selectStage(\'' + currentStage + '\')">' + stageNames[currentStage] + '</span>';
            }

            if (isInLibrary) {
                html += '<span>/</span><span class="text-amber-600 flex items-center gap-1"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path></svg>المكتبة</span>';
            } else if (currentSubject) {
                const subjectNames = {
                    'arabic': 'اللغة العربية',
                    'english': 'اللغة الإنجليزية',
                    'math': 'الرياضيات',
                    'social': 'الدراسات الاجتماعية',
                    'science': 'العلوم',
                    'ict': 'الكمبيوتر (ICT)'
                };
                html += '<span>/</span><span class="text-blue-600">' + subjectNames[currentSubject] + '</span>';
            }

            breadcrumb.innerHTML = html;
        }

        // Authentication functions
        function showLogin() {
            document.getElementById('loginModal').classList.remove('hidden');
            document.getElementById('loginModal').classList.add('flex');
            clearMessages();
        }

        function hideLogin() {
            document.getElementById('loginModal').classList.add('hidden');
            document.getElementById('loginModal').classList.remove('flex');
            clearMessages();
        }

        function toggleAuthMode() {
            isLoginMode = !isLoginMode;
            const modalTitle = document.getElementById('modalTitle');
            const submitBtn = document.getElementById('submitBtn');
            const toggleBtn = document.getElementById('toggleMode');
            const fullNameField = document.getElementById('fullNameField');
            const userTypeField = document.getElementById('userTypeField');

            if (isLoginMode) {
                modalTitle.textContent = 'تسجيل الدخول';
                submitBtn.textContent = 'تسجيل الدخول';
                toggleBtn.textContent = 'ليس لديك حساب؟ أنشئ حساباً جديداً';
                fullNameField.classList.add('hidden');
                userTypeField.classList.add('hidden');
            } else {
                modalTitle.textContent = 'إنشاء حساب جديد';
                submitBtn.textContent = 'إنشاء الحساب';
                toggleBtn.textContent = 'لديك حساب بالفعل؟ سجل الدخول';
                fullNameField.classList.remove('hidden');
                userTypeField.classList.remove('hidden');
            }
            clearMessages();
        }

        function clearMessages() {
            document.getElementById('errorMessage').classList.add('hidden');
            document.getElementById('successMessage').classList.add('hidden');
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.classList.remove('hidden');
        }

        // Handle form submission
        document.getElementById('authForm').addEventListener('submit', function(e) {
            e.preventDefault();
            clearMessages();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showError('يرجى إدخال جميع البيانات المطلوبة');
                return;
            }

            if (isLoginMode) {
                handleLogin(email, password);
            } else {
                const fullName = document.getElementById('fullName').value;
                const userType = document.getElementById('userType').value;

                if (!fullName) {
                    showError('يرجى إدخال الاسم الكامل');
                    return;
                }

                handleRegister(email, password, fullName, userType);
            }
        });

        function handleLogin(email, password) {
            // Get users from localStorage
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const user = users.find(u => u.email === email && u.password === password);

            if (user) {
                saveUserToStorage(user);
                showSuccess('تم تسجيل الدخول بنجاح!');
                setTimeout(() => {
                    hideLogin();
                    updateUI();
                }, 1500);
            } else {
                showError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
            }
        }

        function handleRegister(email, password, fullName, userType) {
            // Get users from localStorage
            const users = JSON.parse(localStorage.getItem('users') || '[]');

            // Check if email already exists
            if (users.find(u => u.email === email)) {
                showError('هذا البريد الإلكتروني مسجل بالفعل');
                return;
            }

            // Create new user
            const newUser = {
                id: Date.now().toString(),
                email,
                password,
                fullName,
                userType,
                createdAt: new Date().toISOString()
            };

            users.push(newUser);
            localStorage.setItem('users', JSON.stringify(users));

            saveUserToStorage(newUser);
            showSuccess('تم إنشاء الحساب بنجاح!');
            setTimeout(() => {
                hideLogin();
                updateUI();
            }, 1500);
        }

        // UI Update functions
        function updateUI() {
            const userSection = document.getElementById('userSection');
            const uploadBtn = document.getElementById('uploadBtn');
            const analyticsBtn = document.getElementById('analyticsBtn');
            const addBookBtn = document.getElementById('addBookBtn');

            if (currentUser) {
                // Update user section to show user info and logout
                userSection.innerHTML = `
                    <div class="flex items-center gap-3">
                        <div class="flex items-center gap-2 bg-gray-100 rounded-lg px-3 py-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-bold">${currentUser.fullName.charAt(0)}</span>
                            </div>
                            <div class="toolbar-user-info">
                                <p class="text-sm font-medium text-gray-900">${currentUser.fullName}</p>
                                <p class="text-xs text-gray-500">${getUserTypeLabel(currentUser.userType)}</p>
                            </div>
                        </div>
                        <button onclick="logout()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg transition-colors flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            <span class="toolbar-btn-text">خروج</span>
                        </button>
                    </div>
                `;

                // Show buttons for teachers and admins
                if (currentUser.userType === 'teacher' || currentUser.userType === 'admin') {
                    uploadBtn.classList.remove('hidden');
                    analyticsBtn.classList.remove('hidden');
                    addBookBtn.classList.remove('hidden');
                }
            } else {
                userSection.innerHTML = `
                    <button onclick="showLogin()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1"></path>
                        </svg>
                        <span class="toolbar-btn-text">تسجيل الدخول</span>
                    </button>
                `;

                uploadBtn.classList.add('hidden');
                analyticsBtn.classList.add('hidden');
                addBookBtn.classList.add('hidden');
            }
        }

        function getUserTypeLabel(userType) {
            const labels = {
                'student': 'طالب',
                'teacher': 'معلم',
                'admin': 'إدارة'
            };
            return labels[userType] || userType;
        }

        function showUploadButton() {
            // Upload button is now handled in updateUI function
            // This function is kept for compatibility
        }

        function hideUploadButton() {
            // Upload button is now handled in updateUI function
            // This function is kept for compatibility
        }

        // Content management
        function initializeContent() {
            // Initialize with some sample content
            if (!localStorage.getItem('contentData')) {
                const sampleContent = {
                    'primary-arabic-pdf': [
                        { id: 1, title: 'كتاب القراءة للصف الأول', description: 'كتاب تعليم القراءة للمبتدئين', type: 'pdf', url: 'https://drive.google.com/file/d/1example/view', fileSize: 2048000, author: 'المعلم أحمد', createdAt: '2024-01-15' },
                        { id: 2, title: 'كتاب الكتابة للصف الثاني', description: 'تعليم الكتابة والخط العربي', type: 'pdf', url: 'https://drive.google.com/file/d/2example/view', fileSize: 1536000, author: 'المعلمة فاطمة', createdAt: '2024-01-20' }
                    ],
                    'primary-arabic-video': [
                        { id: 3, title: 'درس الحروف الأبجدية', description: 'شرح الحروف العربية بالصوت والصورة', type: 'video', url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', author: 'المعلم محمد', createdAt: '2024-01-25' },
                        { id: 4, title: 'تعلم كتابة الحروف', description: 'فيديو تعليمي لكتابة الحروف العربية', type: 'video', url: 'https://www.youtube.com/watch?v=example2', author: 'المعلمة سارة', createdAt: '2024-02-01' }
                    ],
                    'primary-arabic-audio': [
                        { id: 5, title: 'نشيد الحروف', description: 'نشيد تعليمي للحروف العربية', type: 'audio', url: 'https://drive.google.com/file/d/audio1/view', fileSize: 512000, author: 'المعلم خالد', createdAt: '2024-02-05' }
                    ],
                    'primary-arabic-powerpoint': [
                        { id: 6, title: 'عرض تقديمي: الحروف والكلمات', description: 'عرض تفاعلي لتعليم الحروف', type: 'powerpoint', url: 'https://drive.google.com/file/d/ppt1/view', fileSize: 3072000, author: 'المعلمة نور', createdAt: '2024-02-10' }
                    ],
                    'middle-math-pdf': [
                        { id: 7, title: 'كتاب الجبر للصف الثاني الإعدادي', description: 'أساسيات الجبر والمعادلات', type: 'pdf', url: 'https://drive.google.com/file/d/math1/view', fileSize: 4096000, author: 'المعلم علي', createdAt: '2024-02-15' },
                        { id: 8, title: 'مسائل محلولة في الهندسة', description: 'مجموعة من المسائل المحلولة', type: 'pdf', url: 'https://drive.google.com/file/d/math2/view', fileSize: 2560000, author: 'المعلم أحمد', createdAt: '2024-02-20' }
                    ],
                    'middle-math-video': [
                        { id: 9, title: 'شرح المعادلات الخطية', description: 'درس مفصل عن المعادلات الخطية', type: 'video', url: 'https://www.youtube.com/watch?v=math_example', author: 'المعلم محمود', createdAt: '2024-02-25' }
                    ],
                    'secondary-english-pdf': [
                        { id: 10, title: 'English Grammar Guide', description: 'دليل شامل لقواعد اللغة الإنجليزية', type: 'pdf', url: 'https://drive.google.com/file/d/eng1/view', fileSize: 3584000, author: 'المعلمة ليلى', createdAt: '2024-03-01' }
                    ],
                    'secondary-english-audio': [
                        { id: 11, title: 'English Pronunciation Practice', description: 'تمارين النطق الصحيح للإنجليزية', type: 'audio', url: 'https://drive.google.com/file/d/eng_audio/view', fileSize: 1024000, author: 'المعلم جون', createdAt: '2024-03-05' }
                    ]
                };
                localStorage.setItem('contentData', JSON.stringify(sampleContent));
            }
            contentData = JSON.parse(localStorage.getItem('contentData'));
        }

        function showContent(contentType) {
            if (!currentStage || !currentSubject) return;

            // Special handling for tests
            if (contentType === 'tests') {
                window.location.href = 'quiz.html';
                return;
            }

            const key = `${currentStage}-${currentSubject}-${contentType}`;
            const content = contentData[key] || [];

            const contentTitle = document.getElementById('contentTitle');
            const contentBody = document.getElementById('contentBody');

            const typeNames = {
                'pdf': 'كتب PDF',
                'video': 'فيديوهات تعليمية',
                'audio': 'ملفات صوتية',
                'image': 'صور توضيحية',
                'tests': 'اختبارات تفاعلية'
            };

            contentTitle.textContent = typeNames[contentType] || 'المحتوى';

            if (content.length === 0) {
                contentBody.innerHTML = `
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-600 mb-2">لا يوجد محتوى متاح</h3>
                        <p class="text-gray-500 mb-4">لم يتم رفع أي محتوى في هذا القسم بعد</p>
                        ${currentUser && (currentUser.userType === 'teacher' || currentUser.userType === 'admin') ?
                            `<button onclick="showUploadModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center gap-2 mx-auto">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                رفع محتوى جديد
                            </button>` :
                            '<p class="text-gray-400 text-sm">تواصل مع معلمك لإضافة محتوى في هذا القسم</p>'
                        }
                    </div>
                `;
            } else {
                let html = '<div class="grid gap-4">';
                content.forEach(item => {
                    html += `
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-800 mb-2">${item.title}</h4>
                                    <p class="text-gray-600 text-sm mb-2">${item.description}</p>
                                    <div class="flex items-center gap-4 text-xs text-gray-500">
                                        <span>بواسطة: ${item.author}</span>
                                        <span class="flex items-center gap-1">
                                            ${getContentTypeIcon(item.type)}
                                            ${getContentTypeLabel(item.type)}
                                        </span>
                                        ${item.fileSize ? `<span>📁 ${formatFileSize(item.fileSize)}</span>` : ''}
                                    </div>
                                </div>
                                <div class="flex gap-2 flex-wrap">
                                    ${item.url !== '#' ?
                                        `<button onclick="openContent('${item.url}', '${item.type}')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            ${item.type === 'video' ? 'مشاهدة' : item.type === 'audio' ? 'استماع' : 'فتح'}
                                        </button>
                                        <button onclick="downloadContent('${item.url}', '${item.title}', '${item.type}')" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            تحميل
                                        </button>` :
                                        '<span class="text-gray-400 text-sm">قريباً</span>'
                                    }
                                    ${currentUser && (currentUser.userType === 'admin' || (currentUser.userType === 'teacher' && item.author === currentUser.fullName)) ?
                                        `<button onclick="deleteContent(${item.id}, '${key}')" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                            حذف
                                        </button>` :
                                        ''
                                    }
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';

                if (currentUser && (currentUser.userType === 'teacher' || currentUser.userType === 'admin')) {
                    html += `
                        <div class="mt-6 text-center">
                            <button onclick="showUploadModal()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg">
                                + إضافة محتوى جديد
                            </button>
                        </div>
                    `;
                }

                contentBody.innerHTML = html;
            }

            document.getElementById('contentModal').classList.remove('hidden');
            document.getElementById('contentModal').classList.add('flex');
        }

        function hideContentModal() {
            document.getElementById('contentModal').classList.add('hidden');
            document.getElementById('contentModal').classList.remove('flex');
        }

        function getContentTypeIcon(type) {
            const icons = {
                'pdf': '📄',
                'video': '🎥',
                'audio': '🎵',
                'image': '🖼️',
                'powerpoint': '📊'
            };
            return icons[type] || '📁';
        }

        function getContentTypeLabel(type) {
            const labels = {
                'pdf': 'PDF',
                'video': 'فيديو',
                'audio': 'صوت',
                'image': 'صورة',
                'powerpoint': 'عرض تقديمي'
            };
            return labels[type] || 'ملف';
        }

        function formatFileSize(bytes) {
            if (!bytes) return '';
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }

        function openContent(url, type) {
            if (url.includes('youtube.com') || url.includes('youtu.be')) {
                // فتح فيديو YouTube في نافذة جديدة
                window.open(url, '_blank');
            } else if (url.includes('drive.google.com')) {
                // فتح ملف Google Drive في نافذة جديدة
                window.open(url, '_blank');
            } else if (type === 'pdf') {
                // فتح PDF في نافذة جديدة مع عارض PDF
                window.open(url, '_blank');
            } else if (type === 'image') {
                // عرض الصورة في modal
                showImageModal(url);
            } else if (type === 'audio') {
                // تشغيل الصوت في modal
                showAudioModal(url);
            } else if (type === 'video') {
                // تشغيل الفيديو في modal
                showVideoModal(url);
            } else {
                // فتح أي نوع آخر في نافذة جديدة
                window.open(url, '_blank');
            }
        }

        function downloadContent(url, title, type) {
            if (url.includes('youtube.com') || url.includes('youtu.be')) {
                // لا يمكن تحميل فيديوهات YouTube مباشرة
                showNotification('لا يمكن تحميل فيديوهات YouTube مباشرة. يمكنك مشاهدتها عبر الرابط.', 'info');
                return;
            }

            if (url.includes('drive.google.com')) {
                // محاولة تحويل رابط Google Drive للتحميل المباشر
                const fileId = extractGoogleDriveFileId(url);
                if (fileId) {
                    const downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;
                    downloadFile(downloadUrl, title, type);
                } else {
                    window.open(url, '_blank');
                }
                return;
            }

            // تحميل الملف مباشرة
            downloadFile(url, title, type);
        }

        function extractGoogleDriveFileId(url) {
            const match = url.match(/\/d\/([a-zA-Z0-9-_]+)/);
            return match ? match[1] : null;
        }

        function downloadFile(url, title, type) {
            try {
                const link = document.createElement('a');
                link.href = url;
                link.download = title + getFileExtension(type);
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                // إذا فشل التحميل المباشر، افتح في نافذة جديدة
                window.open(url, '_blank');
            }
        }

        function getFileExtension(type) {
            const extensions = {
                'pdf': '.pdf',
                'video': '.mp4',
                'audio': '.mp3',
                'image': '.jpg',
                'powerpoint': '.pptx'
            };
            return extensions[type] || '';
        }

        // Modal functions for media content
        function showImageModal(url) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="max-w-4xl max-h-full p-4">
                    <div class="relative">
                        <img src="${url}" class="max-w-full max-h-full object-contain" alt="صورة">
                        <button onclick="this.closest('.fixed').remove()" class="absolute top-2 right-2 bg-white rounded-full p-2 hover:bg-gray-100">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // إغلاق عند النقر خارج الصورة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        function showAudioModal(url) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">تشغيل الملف الصوتي</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <audio controls class="w-full">
                        <source src="${url}" type="audio/mpeg">
                        <source src="${url}" type="audio/wav">
                        <source src="${url}" type="audio/ogg">
                        متصفحك لا يدعم تشغيل الملفات الصوتية.
                    </audio>
                </div>
            `;
            document.body.appendChild(modal);

            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        function showVideoModal(url) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="max-w-4xl max-h-full p-4">
                    <div class="relative bg-black rounded-lg overflow-hidden">
                        <video controls class="w-full h-auto max-h-[80vh]">
                            <source src="${url}" type="video/mp4">
                            <source src="${url}" type="video/webm">
                            <source src="${url}" type="video/ogg">
                            متصفحك لا يدعم تشغيل الفيديو.
                        </video>
                        <button onclick="this.closest('.fixed').remove()" class="absolute top-2 right-2 bg-white rounded-full p-2 hover:bg-gray-100">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        function deleteContent(contentId, key) {
            if (confirm('هل أنت متأكد من حذف هذا المحتوى؟')) {
                contentData[key] = contentData[key].filter(item => item.id !== contentId);
                localStorage.setItem('contentData', JSON.stringify(contentData));
                showContent(key.split('-')[2]); // Refresh the content view
            }
        }

        // Upload modal functions
        function showUploadModal() {
            if (!currentUser || (currentUser.userType !== 'teacher' && currentUser.userType !== 'admin')) {
                alert('يجب تسجيل الدخول كمعلم أو إدارة لرفع المحتوى');
                return;
            }

            if (!currentStage || !currentSubject) {
                alert('يرجى اختيار المرحلة والمادة أولاً');
                return;
            }

            document.getElementById('uploadModal').classList.remove('hidden');
            document.getElementById('uploadModal').classList.add('flex');
        }

        function hideUploadModal() {
            document.getElementById('uploadModal').classList.add('hidden');
            document.getElementById('uploadModal').classList.remove('flex');
            document.getElementById('uploadForm').reset();
        }

        // Handle upload form submission
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const title = document.getElementById('contentTitleInput').value;
            const description = document.getElementById('contentDescription').value;
            const contentType = document.getElementById('contentTypeSelect').value;
            const file = document.getElementById('fileInput').files[0];
            const url = document.getElementById('urlInput').value;

            if (!title || !contentType) {
                alert('يرجى إدخال العنوان ونوع المحتوى');
                return;
            }

            if (!file && !url) {
                showNotification('يرجى اختيار ملف أو إدخال رابط', 'warning');
                return;
            }

            // التحقق من نوع الملف
            if (file) {
                const validTypes = {
                    'pdf': ['.pdf'],
                    'powerpoint': ['.ppt', '.pptx'],
                    'video': ['.mp4', '.webm', '.avi', '.mov'],
                    'audio': ['.mp3', '.wav', '.ogg', '.m4a'],
                    'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
                };

                const fileName = file.name.toLowerCase();
                const fileExtension = fileName.substring(fileName.lastIndexOf('.'));

                if (!validTypes[contentType] || !validTypes[contentType].includes(fileExtension)) {
                    showNotification(`نوع الملف غير متوافق مع نوع المحتوى المحدد. الأنواع المدعومة: ${validTypes[contentType]?.join(', ')}`, 'error');
                    return;
                }
            }

            const key = `${currentStage}-${currentSubject}-${contentType}`;
            if (!contentData[key]) {
                contentData[key] = [];
            }

            let fileUrl = url;
            let fileSize = null;

            // إذا كان ملف محلي، إنشاء URL مؤقت
            if (file) {
                fileUrl = URL.createObjectURL(file);
                fileSize = file.size;

                // حفظ الملف في localStorage كـ base64 للملفات الصغيرة فقط
                if (file.size < 5 * 1024 * 1024) { // أقل من 5 ميجابايت
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const newContent = {
                            id: Date.now(),
                            title,
                            description,
                            type: contentType,
                            url: e.target.result, // base64 data URL
                            fileSize: fileSize,
                            fileName: file.name,
                            author: currentUser.fullName,
                            createdAt: new Date().toISOString()
                        };

                        contentData[key].push(newContent);
                        localStorage.setItem('contentData', JSON.stringify(contentData));

                        showNotification('تم رفع المحتوى بنجاح! 🎉', 'success');
                        hideUploadModal();
                    };
                    reader.readAsDataURL(file);
                    return;
                } else {
                    showNotification('حجم الملف كبير جداً. يرجى استخدام رابط خارجي أو ملف أصغر (أقل من 5 ميجابايت).', 'warning');
                    return;
                }
            }

            const newContent = {
                id: Date.now(),
                title,
                description,
                type: contentType,
                url: fileUrl,
                fileSize: fileSize,
                author: currentUser.fullName,
                createdAt: new Date().toISOString()
            };

            contentData[key].push(newContent);
            localStorage.setItem('contentData', JSON.stringify(contentData));

            showNotification('تم رفع المحتوى بنجاح! 🎉', 'success');
            hideUploadModal();
        });

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

            const colors = {
                'success': 'bg-green-500 text-white',
                'error': 'bg-red-500 text-white',
                'warning': 'bg-yellow-500 text-white',
                'info': 'bg-blue-500 text-white'
            };

            notification.className += ` ${colors[type] || colors.info}`;
            notification.innerHTML = `
                <div class="flex items-center gap-3">
                    <span class="flex-1">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Event listeners for content type clicks
        document.addEventListener('click', function(e) {
            if (e.target.closest('[data-content-type]')) {
                const contentType = e.target.closest('[data-content-type]').getAttribute('data-content-type');
                showContent(contentType);
            }
        });

        // Close modals when clicking outside
        document.getElementById('loginModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideLogin();
            }
        });

        document.getElementById('contentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideContentModal();
            }
        });

        document.getElementById('uploadModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideUploadModal();
            }
        });

        // Close audio modals when clicking outside
        document.getElementById('addAudioModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAddAudioModal();
            }
        });

        document.getElementById('audioPlayerModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAudioPlayerModal();
            }
        });

        // Close analytics modal when clicking outside
        document.getElementById('analyticsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAnalytics();
            }
        });

        // Add event listeners for analytics filters
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const stageFilter = document.getElementById('stageFilter');
                const subjectAnalyticsFilter = document.getElementById('subjectAnalyticsFilter');
                const timeFilter = document.getElementById('timeFilter');

                if (stageFilter) {
                    stageFilter.addEventListener('change', loadAnalyticsData);
                }

                if (subjectAnalyticsFilter) {
                    subjectAnalyticsFilter.addEventListener('change', loadAnalyticsData);
                }

                if (timeFilter) {
                    timeFilter.addEventListener('change', loadAnalyticsData);
                }
            }, 1000);
        });
    </script>
</body>
</html>