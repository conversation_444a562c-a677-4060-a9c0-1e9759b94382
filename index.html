<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتبة مدرسة White Land الإلكترونية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="text-center mb-12 pt-12">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">
            مكتبة مدرسة White Land الإلكترونية
        </h1>
        <p class="text-lg text-gray-600">
            المكتبة التفاعلية الشاملة لجميع المراحل التعليمية
        </p>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto p-6">
        <!-- Stages Selection -->
        <div class="grid md:grid-cols-3 gap-6" id="stages">
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectStage('primary')">
                <div class="bg-blue-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    المرحلة الابتدائية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectStage('middle')">
                <div class="bg-green-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    المرحلة الإعدادية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectStage('secondary')">
                <div class="bg-purple-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    المرحلة الثانوية
                </h3>
            </div>
        </div>

        <!-- Subjects Selection (Hidden initially) -->
        <div class="grid md:grid-cols-3 gap-6 hidden" id="subjects">
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('arabic')">
                <div class="bg-red-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    اللغة العربية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('english')">
                <div class="bg-blue-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    اللغة الإنجليزية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('math')">
                <div class="bg-yellow-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    الرياضيات
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('social')">
                <div class="bg-green-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    الدراسات الاجتماعية
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('science')">
                <div class="bg-indigo-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    العلوم
                </h3>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105" onclick="selectSubject('ict')">
                <div class="bg-gray-500 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-center text-gray-800">
                    الكمبيوتر (ICT)
                </h3>
            </div>
        </div>

        <!-- Content Types (Hidden initially) -->
        <div class="grid md:grid-cols-3 lg:grid-cols-5 gap-6 hidden" id="content">
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105">
                <div class="bg-red-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    كتب PDF
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105">
                <div class="bg-blue-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    فيديوهات تعليمية
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105">
                <div class="bg-green-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    ملفات صوتية
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105">
                <div class="bg-yellow-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    صور توضيحية
                </h4>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105">
                <div class="bg-purple-500 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <h4 class="text-sm font-semibold text-center text-gray-800">
                    اختبارات تفاعلية
                </h4>
            </div>
        </div>

        <!-- Breadcrumb Navigation -->
        <div class="flex items-center gap-2 mb-8 text-sm text-gray-600" id="breadcrumb">
            <span class="cursor-pointer hover:text-blue-600" onclick="goHome()">الرئيسية</span>
        </div>
    </main>

    <!-- Login Button -->
    <div class="fixed top-6 left-6">
        <button onclick="showLogin()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
            تسجيل الدخول
        </button>
    </div>

    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">تسجيل الدخول</h2>
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل بريدك الإلكتروني">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                    <input type="password" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="أدخل كلمة المرور">
                </div>
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors">
                    تسجيل الدخول
                </button>
            </form>
            <div class="mt-4 text-center">
                <button onclick="hideLogin()" class="text-gray-600 hover:text-gray-800">إغلاق</button>
            </div>
        </div>
    </div>

    <script>
        let currentStage = null;
        let currentSubject = null;

        function selectStage(stage) {
            currentStage = stage;
            currentSubject = null;

            document.getElementById('stages').classList.add('hidden');
            document.getElementById('subjects').classList.remove('hidden');
            document.getElementById('content').classList.add('hidden');

            updateBreadcrumb();
        }

        function selectSubject(subject) {
            currentSubject = subject;

            document.getElementById('subjects').classList.add('hidden');
            document.getElementById('content').classList.remove('hidden');

            updateBreadcrumb();
        }

        function goHome() {
            currentStage = null;
            currentSubject = null;

            document.getElementById('stages').classList.remove('hidden');
            document.getElementById('subjects').classList.add('hidden');
            document.getElementById('content').classList.add('hidden');

            updateBreadcrumb();
        }

        function updateBreadcrumb() {
            const breadcrumb = document.getElementById('breadcrumb');
            let html = '<span class="cursor-pointer hover:text-blue-600" onclick="goHome()">الرئيسية</span>';

            if (currentStage) {
                const stageNames = {
                    'primary': 'المرحلة الابتدائية',
                    'middle': 'المرحلة الإعدادية',
                    'secondary': 'المرحلة الثانوية'
                };
                html += '<span>/</span><span class="cursor-pointer hover:text-blue-600" onclick="selectStage(\'' + currentStage + '\')">' + stageNames[currentStage] + '</span>';
            }

            if (currentSubject) {
                const subjectNames = {
                    'arabic': 'اللغة العربية',
                    'english': 'اللغة الإنجليزية',
                    'math': 'الرياضيات',
                    'social': 'الدراسات الاجتماعية',
                    'science': 'العلوم',
                    'ict': 'الكمبيوتر (ICT)'
                };
                html += '<span>/</span><span class="text-blue-600">' + subjectNames[currentSubject] + '</span>';
            }

            breadcrumb.innerHTML = html;
        }

        function showLogin() {
            document.getElementById('loginModal').classList.remove('hidden');
            document.getElementById('loginModal').classList.add('flex');
        }

        function hideLogin() {
            document.getElementById('loginModal').classList.add('hidden');
            document.getElementById('loginModal').classList.remove('flex');
        }

        // Close modal when clicking outside
        document.getElementById('loginModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideLogin();
            }
        });
    </script>
</body>
</html>