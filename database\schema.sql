-- White Land Library Database Schema

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- <PERSON>reate custom types
CREATE TYPE user_type AS ENUM ('student', 'teacher', 'admin');
CREATE TYPE content_type AS ENUM ('pdf', 'video', 'audio', 'image');
CREATE TYPE question_type AS ENUM ('multiple_choice', 'true_false', 'fill_blank');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    user_type user_type NOT NULL DEFAULT 'student',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Educational stages table
CREATE TABLE public.stages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_en TEXT NOT NULL,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subjects table
CREATE TABLE public.subjects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_en TEXT NOT NULL,
    stage_id UUID REFERENCES public.stages(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content table
CREATE TABLE public.content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    content_type content_type NOT NULL,
    file_url TEXT NOT NULL,
    file_size BIGINT,
    subject_id UUID REFERENCES public.subjects(id) ON DELETE CASCADE,
    uploaded_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tests table
CREATE TABLE public.tests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    subject_id UUID REFERENCES public.subjects(id) ON DELETE CASCADE,
    created_by UUID REFERENCES public.users(id),
    total_questions INTEGER DEFAULT 0,
    time_limit INTEGER, -- in minutes
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Questions table
CREATE TABLE public.questions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID REFERENCES public.tests(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type question_type NOT NULL,
    options JSONB, -- for multiple choice options
    correct_answer TEXT NOT NULL,
    points INTEGER DEFAULT 1,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Test results table
CREATE TABLE public.test_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    test_id UUID REFERENCES public.tests(id) ON DELETE CASCADE,
    student_id UUID REFERENCES public.users(id),
    score INTEGER NOT NULL,
    total_points INTEGER NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    answers JSONB NOT NULL -- stores student answers
);

-- Insert initial data
INSERT INTO public.stages (name, name_en, order_index) VALUES
('المرحلة الابتدائية', 'Primary', 1),
('المرحلة الإعدادية', 'Middle', 2),
('المرحلة الثانوية', 'Secondary', 3);

-- Get stage IDs for subjects
DO $$
DECLARE
    primary_id UUID;
    middle_id UUID;
    secondary_id UUID;
BEGIN
    SELECT id INTO primary_id FROM public.stages WHERE name_en = 'Primary';
    SELECT id INTO middle_id FROM public.stages WHERE name_en = 'Middle';
    SELECT id INTO secondary_id FROM public.stages WHERE name_en = 'Secondary';

    -- Insert subjects for each stage
    INSERT INTO public.subjects (name, name_en, stage_id, order_index) VALUES
    -- Primary subjects
    ('اللغة العربية', 'Arabic', primary_id, 1),
    ('اللغة الإنجليزية', 'English', primary_id, 2),
    ('الرياضيات', 'Mathematics', primary_id, 3),
    ('الدراسات الاجتماعية', 'Social Studies', primary_id, 4),
    ('العلوم', 'Science', primary_id, 5),
    ('الكمبيوتر (ICT)', 'ICT', primary_id, 6),
    
    -- Middle subjects
    ('اللغة العربية', 'Arabic', middle_id, 1),
    ('اللغة الإنجليزية', 'English', middle_id, 2),
    ('الرياضيات', 'Mathematics', middle_id, 3),
    ('الدراسات الاجتماعية', 'Social Studies', middle_id, 4),
    ('العلوم', 'Science', middle_id, 5),
    ('الكمبيوتر (ICT)', 'ICT', middle_id, 6),
    
    -- Secondary subjects
    ('اللغة العربية', 'Arabic', secondary_id, 1),
    ('اللغة الإنجليزية', 'English', secondary_id, 2),
    ('الرياضيات', 'Mathematics', secondary_id, 3),
    ('الدراسات الاجتماعية', 'Social Studies', secondary_id, 4),
    ('العلوم', 'Science', secondary_id, 5),
    ('الكمبيوتر (ICT)', 'ICT', secondary_id, 6);
END $$;

-- Row Level Security Policies

-- Users table policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Stages and subjects are readable by all authenticated users
ALTER TABLE public.stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subjects ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can view stages" ON public.stages
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view subjects" ON public.subjects
    FOR SELECT USING (auth.role() = 'authenticated');

-- Content policies
ALTER TABLE public.content ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can view content" ON public.content
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Teachers and admins can manage content" ON public.content
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type IN ('teacher', 'admin')
        )
    );

-- Tests policies
ALTER TABLE public.tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.test_results ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can view tests" ON public.tests
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Teachers can manage their tests" ON public.tests
    FOR ALL USING (
        auth.uid() = created_by OR
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

CREATE POLICY "Authenticated users can view questions" ON public.questions
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Test creators can manage questions" ON public.questions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.tests 
            WHERE id = test_id AND created_by = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

CREATE POLICY "Students can view their own results" ON public.test_results
    FOR SELECT USING (
        auth.uid() = student_id OR
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND user_type IN ('teacher', 'admin')
        )
    );

CREATE POLICY "Students can insert their own results" ON public.test_results
    FOR INSERT WITH CHECK (auth.uid() = student_id);

-- Functions and triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_updated_at BEFORE UPDATE ON public.content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tests_updated_at BEFORE UPDATE ON public.tests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
