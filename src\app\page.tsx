'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { BookOpen, Users, GraduationCap, Settings, FileText, Video, Headphones, Image, TestTube } from 'lucide-react'

export default function Home() {
  const [selectedStage, setSelectedStage] = useState<string | null>(null)
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const router = useRouter()

  const stages = [
    { id: 'primary', name: 'المرحلة الابتدائية', icon: BookOpen, color: 'bg-blue-500' },
    { id: 'middle', name: 'المرحلة الإعدادية', icon: Users, color: 'bg-green-500' },
    { id: 'secondary', name: 'المرحلة الثانوية', icon: GraduationCap, color: 'bg-purple-500' }
  ]

  const subjects = [
    { id: 'arabic', name: 'اللغة العربية', icon: FileText, color: 'bg-red-500' },
    { id: 'english', name: 'اللغة الإنجليزية', icon: BookOpen, color: 'bg-blue-500' },
    { id: 'math', name: 'الرياضيات', icon: Settings, color: 'bg-yellow-500' },
    { id: 'social', name: 'الدراسات الاجتماعية', icon: Users, color: 'bg-green-500' },
    { id: 'science', name: 'العلوم', icon: TestTube, color: 'bg-indigo-500' },
    { id: 'ict', name: 'الكمبيوتر (ICT)', icon: Settings, color: 'bg-gray-500' }
  ]

  const contentTypes = [
    { id: 'pdf', name: 'كتب PDF', icon: FileText, color: 'bg-red-500' },
    { id: 'video', name: 'فيديوهات تعليمية', icon: Video, color: 'bg-blue-500' },
    { id: 'audio', name: 'ملفات صوتية', icon: Headphones, color: 'bg-green-500' },
    { id: 'images', name: 'صور توضيحية', icon: Image, color: 'bg-yellow-500' },
    { id: 'tests', name: 'اختبارات تفاعلية', icon: TestTube, color: 'bg-purple-500' }
  ]

  return (
    <div className="min-h-screen p-6">
      {/* Header */}
      <header className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          مكتبة مدرسة White Land الإلكترونية
        </h1>
        <p className="text-lg text-gray-600">
          المكتبة التفاعلية الشاملة لجميع المراحل التعليمية
        </p>
      </header>

      {/* Navigation Breadcrumb */}
      <div className="flex items-center gap-2 mb-8 text-sm text-gray-600">
        <span 
          className="cursor-pointer hover:text-blue-600"
          onClick={() => {
            setSelectedStage(null)
            setSelectedSubject(null)
          }}
        >
          الرئيسية
        </span>
        {selectedStage && (
          <>
            <span>/</span>
            <span 
              className="cursor-pointer hover:text-blue-600"
              onClick={() => setSelectedSubject(null)}
            >
              {stages.find(s => s.id === selectedStage)?.name}
            </span>
          </>
        )}
        {selectedSubject && (
          <>
            <span>/</span>
            <span className="text-blue-600">
              {subjects.find(s => s.id === selectedSubject)?.name}
            </span>
          </>
        )}
      </div>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto">
        {!selectedStage ? (
          /* Stages Selection */
          <div className="grid md:grid-cols-3 gap-6">
            {stages.map((stage) => {
              const Icon = stage.icon
              return (
                <div
                  key={stage.id}
                  className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                  onClick={() => setSelectedStage(stage.id)}
                >
                  <div className={`${stage.color} w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-center text-gray-800">
                    {stage.name}
                  </h3>
                </div>
              )
            })}
          </div>
        ) : !selectedSubject ? (
          /* Subjects Selection */
          <div className="grid md:grid-cols-3 gap-6">
            {subjects.map((subject) => {
              const Icon = subject.icon
              return (
                <div
                  key={subject.id}
                  className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                  onClick={() => setSelectedSubject(subject.id)}
                >
                  <div className={`${subject.color} w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-center text-gray-800">
                    {subject.name}
                  </h3>
                </div>
              )
            })}
          </div>
        ) : (
          /* Content Types */
          <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-6">
            {contentTypes.map((content) => {
              const Icon = content.icon
              return (
                <div
                  key={content.id}
                  className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                >
                  <div className={`${content.color} w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-sm font-semibold text-center text-gray-800">
                    {content.name}
                  </h4>
                </div>
              )
            })}
          </div>
        )}
      </main>

      {/* Login Button */}
      <div className="fixed top-6 left-6">
        <button
          onClick={() => router.push('/auth')}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200"
        >
          تسجيل الدخول
        </button>
      </div>
    </div>
  )
}
